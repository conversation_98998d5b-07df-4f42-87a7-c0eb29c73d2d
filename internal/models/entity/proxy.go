package entity

import (
	"time"
)

// Proxy 代理实体
type Proxy struct {
	ID           uint      `gorm:"primarykey" json:"id"`
	Name         string    `gorm:"column:name;type:varchar(255);not null;default:'';comment:代理名称" json:"name"` // 代理名称
	Hosts        string    `gorm:"column:hosts;not null" json:"hosts"`                  // 代理主机
	Port         string    `gorm:"column:port;not null" json:"port"`                    // 代理端口
	Username     string    `gorm:"column:username" json:"username"`                     // 用户名
	Password     string    `gorm:"column:password" json:"password"`                     // 密码
	Group        string    `gorm:"column:group" json:"group"`                           // 分组
	Status       string    `gorm:"column:status;default:'healthy'" json:"status"`       // 状态：healthy, unhealthy, disabled
	LastUsed     time.Time `gorm:"column:last_used" json:"last_used"`                   // 最后使用时间
	SuccessCount int       `gorm:"column:success_count;default:0" json:"success_count"` // 成功次数
	FailCount    int       `gorm:"column:fail_count;default:0" json:"fail_count"`       // 失败次数
	CreatedAt    time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updated_at"`
}

// TableName 指定表名
func (Proxy) TableName() string {
	return "t_proxy"
}
