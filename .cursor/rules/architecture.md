# 浏览器群控系统架构设计

## 系统概述

本系统是一个基于 Chrome DevTools Protocol (CDP) 的浏览器群控平台，支持一个主控浏览器的操作同步到多个从控浏览器（可跨设备），并具备事件录制、重放和安全随机化功能。

## 核心架构

### 三层架构模式
```
Web控制台 <--HTTP/WebSocket--> 服务端 <--WebSocket--> 客户端 <--CDP--> Chrome浏览器
```

### 技术栈总览
- **后端服务**: Go 1.23+ + Gin + GORM + Gorilla WebSocket
- **数据库**: MySQL 8.0+
- **前端**: HTML5 + Bootstrap 5 + Vanilla JavaScript
- **浏览器控制**: Chrome DevTools Protocol (CDP)
- **通信协议**: WebSocket + HTTP REST API
- **配置管理**: YAML
- **依赖管理**: Go Modules

### 项目结构
```
web3-control/
├── cmd/                    # 程序入口点
│   ├── server/            # 服务端主程序
│   └── client/            # 客户端主程序
├── module/                # 业务模块
│   ├── server/           # 服务端业务逻辑
│   │   ├── handler/      # HTTP/WebSocket处理器
│   │   ├── model/        # 数据模型定义
│   │   ├── service/      # 业务服务层
│   │   └── config/       # 服务端配置
│   └── client/           # 客户端业务逻辑
│       ├── browser/      # 浏览器控制模块
│       ├── event/        # 事件处理模块
│       └── noise/        # 随机化处理模块
├── internal/             # 内部共享库
│   ├── protocol/         # 通信协议定义
│   ├── models/           # 数据结构定义
│   ├── config/           # 配置管理
│   ├── database/         # 数据库连接管理
│   └── utils/            # 工具函数
├── web/                  # Web控制台
│   ├── static/           # 静态资源(CSS/JS/图片)
│   └── template/         # HTML模板
├── configs/              # 配置文件
├── scripts/              # 部署和工具脚本
├── docs/                 # 项目文档
├── server.exe            # 服务端执行文件
├── client.exe            # 客户端执行文件
├── web3.sql            # 数据库文件
└── test/                 # 测试文件
```

## 数据库设计
```sql
CREATE TABLE `t_browser` (
   `id` int NOT NULL AUTO_INCREMENT,
   `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '浏览器多开窗口名称',
   `client_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '绑定的客户端ID',
   `group` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '浏览器多开窗口分组',
   `proxy` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '使用的代理',
   `user_dir` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户资料目录',
   `port` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'CDP调试端口',
   `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
   `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
   `deleted_at` datetime(3) DEFAULT NULL,
   `browser_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '浏览器多开窗口标识',
   `user_agent` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户代理',
   `window_size` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '窗口大小',
   `headless` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否无头模式',
   `disable_gpu` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否禁用GPU',
   PRIMARY KEY (`id`),
   UNIQUE KEY `idx_t_browser_browser_id` (`browser_id`),
   KEY `idx_t_browser_client_id` (`client_id`),
   KEY `idx_t_browser_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='浏览器窗口配置';

CREATE TABLE `t_browser_status` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `browser_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '浏览器多开窗口标识',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '错误信息',
  `url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '当前URL',
  `memory_usage` float(10,2) NOT NULL DEFAULT '0.00' COMMENT '内存使用(MB)',
  `cpu_usage` float(10,2) NOT NULL DEFAULT '0.00' COMMENT 'CPU使用(%)',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_t_browser_status_browser_id` (`browser_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='浏览器状态表';

CREATE TABLE `t_client` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `client_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端ID',
  `is_master` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否主控客户端',
  `name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户端名称',
  `online` tinyint(1) DEFAULT '0' COMMENT '是否在线',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `group` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '分组',
  `last_online` datetime DEFAULT NULL COMMENT '最后在线时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_t_client_client_id` (`client_id`),
  KEY `idx_t_client_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户端信息表';

CREATE TABLE `t_proxy` (
   `id` int unsigned NOT NULL AUTO_INCREMENT,
   `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '代理名称',
   `hosts` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
   `port` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
   `group` longtext COLLATE utf8mb4_unicode_ci,
   `created_at` datetime(3) DEFAULT NULL,
   `username` longtext COLLATE utf8mb4_unicode_ci,
   `password` longtext COLLATE utf8mb4_unicode_ci,
   `status` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT 'healthy',
   `last_used` datetime(3) DEFAULT NULL,
   `success_count` bigint DEFAULT '0',
   `fail_count` bigint DEFAULT '0',
   `updated_at` datetime(3) DEFAULT NULL,
   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理配置表';

CREATE TABLE `t_proxy_usage` (
   `id` bigint unsigned NOT NULL AUTO_INCREMENT,
   `proxy_id` int unsigned NOT NULL COMMENT '代理ID',
   `browser_id` int unsigned NOT NULL COMMENT '浏览器ID',
   `start_time` datetime NOT NULL COMMENT '开始使用时间',
   `end_time` datetime DEFAULT NULL COMMENT '结束使用时间',
   `success` tinyint(1) DEFAULT '0' COMMENT '是否成功',
   `error_msg` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '错误信息',
   PRIMARY KEY (`id`),
   KEY `idx_t_proxy_usage_proxy_id` (`proxy_id`),
   KEY `idx_t_proxy_usage_browser_id` (`browser_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 核心组件详解

### 1. 服务端 (Server)

**主要职责：**
- **连接管理**: 管理客户端WebSocket连接，维护在线状态
- **认证授权**: 基于client_id和auth_token的客户端认证
- **事件广播**: 接收主控事件并实时广播到从控客户端
- **录制管理**: 事件序列的录制、存储和重放
- **Web控制台**: 提供完整的管理界面
- **浏览器管理**: 管理浏览器配置和启动指令
- **代理管理**: 代理服务器的配置和分配

**核心模块：**
- `cmd/server/main.go`: 服务端启动入口
- `module/server/handler/`: HTTP和WebSocket请求处理
- `module/server/service/`: 业务逻辑层
- `module/server/model/`: 数据模型定义
- `internal/database/`: 数据库连接管理

**关键接口：**
```go
// 客户端管理
GET    /api/clients              // 获取客户端列表
GET    /api/clients/:id          // 获取客户端详情
DELETE /api/clients/:id          // 删除客户端
GET    /api/clients/group/:group // 按分组获取客户端

// 浏览器管理
GET    /api/browsers             // 获取浏览器列表
POST   /api/browsers             // 创建浏览器配置
PUT    /api/browsers/:id         // 更新浏览器配置
DELETE /api/browsers/:id         // 删除浏览器配置
POST   /api/browsers/launch      // 启动浏览器

// 录制管理
GET    /api/recordings           // 获取录制列表
POST   /api/recording/start      // 开始录制
POST   /api/recording/stop       // 停止录制
POST   /api/recording/play       // 播放录制
DELETE /api/recordings/:id       // 删除录制

// WebSocket端点
WS     /ws                       // 客户端连接端点
```

### 2. 客户端 (Client)

**主要职责：**
- **连接维护**: 与服务端建立并维护WebSocket连接
- **浏览器控制**: 通过CDP协议控制Chrome浏览器
- **指令执行**: 执行服务端下发的各种指令
- **状态报告**: 定期上报浏览器运行状态
- **噪声处理**: 为操作添加随机化噪声

**核心模块：**
- `cmd/client/main.go`: 客户端启动入口
- `module/client/browser/`: 浏览器控制逻辑
  - `manager.go`: 浏览器管理器
  - `launcher.go`: 浏览器启动器
  - `controller.go`: 浏览器控制器
- `module/client/event/`: 事件处理逻辑
  - `processor.go`: 事件处理器
  - `executor.go`: 事件执行器
  - `recorder.go`: 事件记录器
- `module/client/noise/`: 随机化处理逻辑
  - `generator.go`: 噪声生成器
  - `humanizer.go`: 人类行为模拟器
- `module/client/connection/`: 连接管理模块
  - `websocket.go`: WebSocket连接管理
  - `auth.go`: 认证处理
- `module/client/config/`: 客户端配置模块
  - `config.go`: 配置管理

**支持的指令类型：**
```go
// 浏览器生命周期管理
type LaunchBrowserCommand struct {
    Command   string        `json:"command"`
    BrowserID string        `json:"browser_id"`
    Config    BrowserConfig `json:"config"`
}

type CloseBrowserCommand struct {
    Command   string `json:"command"`
    BrowserID string `json:"browser_id"`
}

// 操作执行指令
type ExecuteEventCommand struct {
    Command string `json:"command"`
    Event   Event  `json:"event"`
}

// 状态管理指令
type ReportStatusCommand struct {
    Command string `json:"command"`
    Status  string `json:"status"`
}
```

**客户端配置结构：**
```go
type ClientConfig struct {
    Server      string  `yaml:"server"`       // 服务器地址
    ClientID    string  `yaml:"client_id"`    // 客户端ID
    Name        string  `yaml:"name"`         // 客户端名称
    Group       string  `yaml:"group"`        // 分组
    AuthToken   string  `yaml:"auth_token"`   // 认证令牌
    IsMaster    bool    `yaml:"is_master"`    // 是否主控
    NoiseLevel  float64 `yaml:"noise_level"`  // 噪声级别

    Chrome struct {
        Path        string `yaml:"path"`         // Chrome路径
        UserDataDir string `yaml:"user_data_dir"` // 用户数据目录
        DebugPort   int    `yaml:"debug_port"`   // 调试端口
    } `yaml:"chrome"`
}
```

**命令行参数：**
```bash
--server=ws://localhost:8080/ws  # 服务器WebSocket地址
--client-id=CLIENT-ID            # 客户端ID（可选，自动生成）
--name="客户端名称"               # 客户端名称
--group=group1                   # 分组名称
--auth=secret-key                # 认证密钥
--is-master=false                # 是否主控客户端
--config=configs/client.yaml     # 配置文件路径
--noise=0.2                      # 噪声级别(0-1)
--chrome-path=""                 # Chrome可执行文件路径
--user-data-dir=""               # Chrome用户数据目录
--debug-port=9222                # Chrome调试端口
```

### 3. Web控制台 (Frontend)

**主要功能：**
- **客户端管理**: 查看、添加、删除客户端
- **浏览器管理**: 配置浏览器参数、批量启动
- **录制管理**: 开始/停止录制、播放录制
- **代理管理**: 配置和分配代理服务器
- **实时监控**: 显示客户端和浏览器状态

**技术实现：**
- **模板引擎**: Go template
- **前端框架**: Bootstrap 5 + Vanilla JavaScript
- **通信方式**: AJAX + WebSocket
- **状态管理**: 本地状态管理

### 4. 主控浏览器注入脚本 (Injection Script)

**主要功能：**
- **事件监听**: 监听用户的点击、输入、滚动等操作
- **事件上报**: 将操作事件实时发送到服务端
- **录制控制**: 支持开始/停止录制功能
- **状态同步**: 与服务端保持录制状态同步

**事件类型：**
- `click`: 鼠标点击事件
- `input`: 输入框输入事件
- `scroll`: 页面滚动事件
- `keypress`: 键盘按键事件
- `focus`: 元素焦点事件

## 系统流程详解

### 1. 客户端注册认证流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 服务端
    participant DB as 数据库

    C->>S: WebSocket连接请求
    C->>S: 发送认证信息 {client_id, auth_token, name, group}
    S->>S: 验证auth_token
    alt 认证成功
        S->>DB: 更新客户端在线状态
        S->>C: 认证成功响应
        S->>S: 将客户端加入在线列表
    else 认证失败
        S->>C: 认证失败响应
        S->>C: 关闭连接
    end
```

### 2. 实时同步流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant MB as 主控浏览器
    participant S as 服务端
    participant SB as 从控浏览器

    U->>MB: 执行操作(点击/输入/滚动)
    MB->>MB: 注入脚本捕获事件
    MB->>S: 发送操作事件 {type, data, timestamp}
    S->>S: 广播事件到从控客户端
    S->>SB: 转发操作事件
    SB->>SB: 添加随机噪声
    SB->>SB: 通过CDP执行操作
```

### 3. 录制重放流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant W as Web控制台
    participant S as 服务端
    participant DB as 数据库
    participant C as 客户端

    U->>W: 点击"开始录制"
    W->>S: POST /api/recording/start
    S->>S: 创建录制会话
    S->>DB: 保存录制信息

    loop 录制过程
        S->>S: 接收主控事件
        S->>DB: 保存事件到录制
    end

    U->>W: 点击"停止录制"
    W->>S: POST /api/recording/stop
    S->>S: 结束录制会话

    U->>W: 选择录制并播放
    W->>S: POST /api/recording/play {recording_id, group}
    S->>DB: 查询录制事件
    S->>C: 按时间序列发送事件
    C->>C: 执行操作(添加噪声)
```

### 4. 浏览器启动流程

```
用户 ---> Web控制台: 勾选客户端或浏览器窗口
用户 ---> Web控制台: 点击“启动浏览器”按钮
服务端: 查询选中的浏览器窗口配置及其绑定的客户端
服务端 ---> 客户端: 发送启动浏览器指令，包含浏览器配置信息
客户端: 根据配置启动本地Chrome浏览器
客户端 ---> 服务端: 发送浏览器启动状态
服务端 ---> Web控制台: 更新浏览器状态显示
```

### 5. 状态监控流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 服务端
    participant DB as 数据库
    participant W as Web控制台

    loop 定期状态上报
        C->>C: 收集浏览器状态(URL、内存、CPU)
        C->>S: 上报状态 {browser_id, status, url, memory_usage, cpu_usage}
        S->>DB: 更新浏览器状态表
        S->>W: 推送状态到Web控制台
    end

    alt 浏览器异常
        C->>S: 上报错误状态 {browser_id, status: "error", error_message}
        S->>DB: 记录错误信息
        S->>W: 显示错误提示
    end
```

## 数据结构详解

### 1. 事件数据结构

**基础事件结构：**
```go
type Event struct {
    Type      string                 `json:"type"`      // 事件类型
    Timestamp int64                  `json:"timestamp"` // 时间戳
    Delay     int                    `json:"delay"`     // 延迟时间(ms)
    Data      map[string]interface{} `json:"data"`      // 事件数据
}
```

**具体事件类型：**
```json
// 点击事件
{
  "type": "click",
  "timestamp": 1649123456789,
  "data": {
    "selector": "#login-button",
    "x": 100,
    "y": 200,
    "button": "left"
  }
}

// 输入事件
{
  "type": "input",
  "timestamp": 1649123456790,
  "data": {
    "selector": "#username",
    "value": "admin",
    "clear": true
  }
}

// 滚动事件
{
  "type": "scroll",
  "timestamp": 1649123456791,
  "data": {
    "x": 0,
    "y": 500,
    "deltaX": 0,
    "deltaY": 500
  }
}

// 键盘事件
{
  "type": "keypress",
  "timestamp": 1649123456792,
  "data": {
    "key": "Enter",
    "code": "Enter",
    "ctrlKey": false,
    "shiftKey": false
  }
}
```

### 2. 客户端认证数据结构

**注册请求：**
```go
type ClientRegisterRequest struct {
    ClientID  string `json:"client_id"`  // 客户端唯一标识
    AuthToken string `json:"auth_token"` // 认证令牌
    Name      string `json:"name"`       // 客户端名称
    Group     string `json:"group"`      // 分组名称
    IsMaster  bool   `json:"is_master"`  // 是否主控
}
```

**认证响应：**
```go
type ClientRegisterResponse struct {
    Code    int    `json:"code"`    // 状态码 0:成功 其他:失败
    Message string `json:"message"` // 响应消息
    Data    struct {
        ClientID string `json:"client_id"`
        Online   bool   `json:"online"`
    } `json:"data"`
}
```

### 3. 录制数据结构

**录制会话：**
```go
type RecordingSession struct {
    ID          uint      `json:"id"`
    Name        string    `json:"name"`        // 录制名称
    Description string    `json:"description"` // 录制描述
    CreatedAt   time.Time `json:"created_at"`  // 创建时间
    Events      []Event   `json:"events"`      // 事件列表
}
```

**录制播放请求：**
```go
type PlayRecordingRequest struct {
    RecordingID uint   `json:"recording_id"` // 录制ID
    Group       string `json:"group"`        // 目标分组(可选)
    ClientIDs   []string `json:"client_ids"` // 目标客户端列表(可选)
    Speed       float64  `json:"speed"`      // 播放速度倍率(默认1.0)
}
```

### 4. 浏览器配置数据结构

**浏览器配置：**
```go
type BrowserConfig struct {
    BrowserID    string   `json:"browser_id"`     // 浏览器唯一标识
    Name         string   `json:"name"`           // 浏览器名称
    ClientID     string   `json:"client_id"`      // 绑定的客户端ID
    Group        string   `json:"group"`          // 分组
    UserDir      string   `json:"user_dir"`       // 用户数据目录
    Proxy        string   `json:"proxy"`          // 代理设置
    Port         string   `json:"port"`           // CDP调试端口
    UserAgent    string   `json:"user_agent"`     // 用户代理
    WindowSize   string   `json:"window_size"`    // 窗口大小
    Headless     bool     `json:"headless"`       // 是否无头模式
    DisableGPU   bool     `json:"disable_gpu"`    // 是否禁用GPU
    AdditionalArgs []string `json:"additional_args"` // 额外启动参数
}
```

**启动浏览器指令：**
```go
type LaunchBrowserCommand struct {
    Command   string        `json:"command"`    // 指令类型: "launch_browser"
    BrowserID string        `json:"browser_id"` // 浏览器ID
    Config    BrowserConfig `json:"config"`     // 浏览器配置
}
```

### 5. 浏览器状态数据结构

**状态报告：**
```go
type BrowserStatus struct {
    BrowserID    string  `json:"browser_id"`    // 浏览器ID
    Status       string  `json:"status"`        // 状态: running/closed/error
    ErrorMessage string  `json:"error_message"` // 错误信息
    URL          string  `json:"url"`           // 当前URL
    MemoryUsage  float64 `json:"memory_usage"`  // 内存使用(MB)
    CPUUsage     float64 `json:"cpu_usage"`     // CPU使用率(%)
    UpdatedAt    time.Time `json:"updated_at"`  // 更新时间
}
```

### 6. WebSocket消息结构

**消息基础结构：**
```go
type WebSocketMessage struct {
    Type      string      `json:"type"`      // 消息类型
    Timestamp int64       `json:"timestamp"` // 时间戳
    Data      interface{} `json:"data"`      // 消息数据
}
```

**消息类型定义：**
- `auth`: 客户端认证
- `event`: 操作事件
- `command`: 服务端指令
- `status`: 状态更新
- `heartbeat`: 心跳检测

## 安全性设计

### 1. 认证与授权
- **客户端认证**: 基于client_id和auth_token的双重认证机制
- **连接加密**: WebSocket通信支持TLS/WSS加密
- **权限控制**: 主控客户端和从控客户端权限分离
- **会话管理**: 支持会话超时和强制下线

### 2. 反检测机制
- **随机噪声**: 为每个从控客户端添加随机延迟、位置偏移
- **人类行为模拟**: 模拟真实的鼠标轨迹、打字速度、停顿时间
- **浏览器指纹随机化**: 随机化User-Agent、窗口大小、屏幕分辨率
- **代理轮换**: 支持代理池自动轮换，避免IP封禁

### 3. 数据安全
- **敏感信息加密**: 数据库中的敏感配置信息加密存储
- **日志脱敏**: 操作日志中的敏感信息脱敏处理
- **访问控制**: Web控制台访问IP白名单限制

## 扩展性设计

### 1. 水平扩展
- **分布式部署**: 支持多服务端实例负载均衡
- **数据库分片**: 支持数据库读写分离和分片
- **消息队列**: 使用消息队列处理高并发事件

### 2. 功能扩展
- **插件系统**: 支持自定义事件处理插件
- **脚本引擎**: 支持JavaScript脚本自定义操作逻辑
- **API扩展**: 提供完整的REST API和WebSocket API
- **第三方集成**: 支持与其他自动化工具集成

### 3. 监控与运维
- **性能监控**: 实时监控系统性能指标
- **日志聚合**: 集中化日志收集和分析
- **告警机制**: 异常情况自动告警
- **健康检查**: 服务健康状态检查

## 开发指导原则

### 1. 代码组织原则
- **模块化设计**: 按功能模块组织代码，保持高内聚低耦合
- **接口抽象**: 定义清晰的接口，便于测试和扩展
- **依赖注入**: 使用依赖注入管理组件依赖关系
- **错误处理**: 统一的错误处理机制和错误码定义

### 2. 数据库设计原则
- **表命名**: 使用`t_`前缀，下划线分隔单词
- **字段命名**: 使用下划线分隔，添加适当注释
- **索引优化**: 为查询频繁的字段添加索引
- **数据迁移**: 使用GORM的AutoMigrate进行数据库迁移

### 3. API设计原则
- **RESTful设计**: 遵循REST API设计规范
- **统一响应格式**: 使用统一的JSON响应格式
- **版本控制**: API版本控制策略
- **文档完整**: 完整的API文档和示例

### 4. 前端开发原则
- **组件化**: 将UI拆分为可复用的组件
- **状态管理**: 合理管理前端状态
- **错误处理**: 友好的错误提示和处理
- **响应式设计**: 支持不同屏幕尺寸

### 5. 测试策略
- **单元测试**: 核心业务逻辑单元测试覆盖
- **集成测试**: 关键流程集成测试
- **性能测试**: 高并发场景性能测试
- **安全测试**: 安全漏洞扫描和测试

## AI代码生成指导

### 1. 生成新功能时的考虑点
- **遵循现有架构**: 新功能应符合三层架构模式
- **数据库设计**: 新表应遵循现有命名规范和字段设计
- **API设计**: 新接口应遵循现有的响应格式和错误处理
- **前端集成**: 新功能的前端应与现有UI风格保持一致

### 2. 常用代码模板
- **Handler模板**: HTTP请求处理器的标准结构
- **Service模板**: 业务逻辑层的标准结构
- **Model模板**: 数据模型的标准结构
- **WebSocket模板**: WebSocket消息处理的标准结构

### 3. 关键依赖包
```go
// Web框架
"github.com/gin-gonic/gin"

// 数据库ORM
"gorm.io/gorm"
"gorm.io/driver/mysql"

// WebSocket
"github.com/gorilla/websocket"

// 配置管理
"gopkg.in/yaml.v3"

// 浏览器控制
"github.com/chromedp/chromedp"
```

### 4. 开发环境要求
- **Go版本**: 1.23+
- **数据库**: MySQL 8.0+
- **浏览器**: Chrome/Chromium (支持CDP)
- **开发工具**: 推荐使用VS Code + Go扩展
