# 项目目录结构规范

## 根目录结构
```
web3-control/
├── cmd/                    # 主程序入口
│   ├── server/            # 服务端程序
│   └── client/            # 客户端程序
├── module/                # 模块目录
│   ├── server/           # 服务端模块
│   │   ├── handler/      # HTTP处理器
│   │   ├── model/        # 数据模型
│   │   ├── service/      # 业务逻辑
│   │   └── config/       # 服务端配置
│   └── client/           # 客户端模块
│       ├── browser/      # 浏览器控制
│       ├── event/        # 事件处理
│       └── noise/        # 随机化处理
├── internal/              # 内部公共库
│   ├── protocol/         # 协议定义
│   ├── utils/            # 工具函数
│   └── common/           # 公共组件
├── web/                   # Web控制台
│   ├── static/           # 静态资源
│   └── template/         # 模板文件
├── configs/              # 配置文件
├── scripts/              # 脚本文件
├── docs/                 # 文档
├── test/                 # 测试文件
├── go.mod               # Go模块文件
├── server.exe            # 服务端执行文件
├── client.exe            # 客户端执行文件
├── web3.sql            # 数据库文件
├── go.sum               # Go依赖版本锁定文件
└── README.md            # 项目说明文档
```

## 目录说明

### cmd/
- 存放主程序入口
- server/: 服务端程序入口
- client/: 客户端程序入口

### module/
- 存放核心模块代码
- server/: 服务端模块
  - handler/: HTTP请求处理器
  - model/: 数据模型定义
  - service/: 业务逻辑实现
  - config/: 服务端配置
- client/: 客户端模块
  - browser/: 浏览器控制相关
  - event/: 事件处理相关
  - noise/: 随机化处理相关

### internal/
- 存放内部公共库
- protocol/: 协议定义
- utils/: 通用工具函数
- common/: 公共组件

### web/
- 存放Web控制台相关文件
- static/: 静态资源（CSS、JS等）
- template/: HTML模板文件

### configs/
- 存放配置文件
- 支持不同环境的配置

### scripts/
- 存放各种脚本文件
- 部署脚本
- 开发工具脚本

### docs/
- 项目文档
- API文档
- 使用说明

### test/
- 测试文件
- 单元测试
- 集成测试

## 命名规范

1. 文件名使用小写字母，单词间用下划线连接
2. 包名使用小写字母，不使用下划线
3. 结构体名称使用驼峰命名
4. 接口名称以er结尾
5. 变量名使用驼峰命名
6. 常量使用大写字母，单词间用下划线连接

## 代码组织原则

1. 相关功能放在同一目录下
2. 避免循环依赖
3. 保持目录结构扁平化
4. 遵循Go标准项目布局
5. 合理使用internal目录保护内部包 