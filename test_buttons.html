<!DOCTYPE html>
<html>
<head>
    <title>测试按钮显示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h3>客户端操作按钮测试</h3>
        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>分组</th>
                    <th>类型</th>
                    <th>状态</th>
                    <th>最后在线</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="clientList">
                <!-- 测试数据 -->
                <tr>
                    <td>skdannoqoewqpjasj</td>
                    <td>主控客户端</td>
                    <td>local</td>
                    <td><span class="badge bg-primary">主控</span></td>
                    <td><span class="badge bg-success">在线</span></td>
                    <td>2025-04-15 21:06:06</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-info" onclick="alert('详情')" title="查看详情">详情</button>
                            <button class="btn btn-outline-warning" onclick="alert('编辑')" title="编辑">编辑</button>
                            <button class="btn btn-outline-danger" onclick="alert('删除')" title="删除">删除</button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <h3>JavaScript函数测试</h3>
        <button class="btn btn-primary" onclick="testUpdateClientList()">测试updateClientList函数</button>
        <div id="testResult" class="mt-3"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试数据
        const testClients = [
            {
                id: 1,
                client_id: "skdannoqoewqpjasj",
                name: "主控客户端",
                group: "local",
                is_master: true,
                online: 1,
                last_online: "2025-04-15T21:06:06+08:00"
            }
        ];

        // 模拟client.js中的函数
        window.clientJsLoaded = true;

        function updateClientList(clients) {
            console.log('updateClientList called with:', clients);
            const tbody = document.getElementById('clientList');
            tbody.innerHTML = '';
            
            clients.forEach(client => {
                const tr = document.createElement('tr');
                const statusBadge = client.online ? 
                    '<span class="badge bg-success">在线</span>' : 
                    '<span class="badge bg-secondary">离线</span>';
                
                const lastOnline = client.last_online ? 
                    new Date(client.last_online).toLocaleString() : 
                    '-';
                
                tr.innerHTML = `
                    <td>${client.client_id}</td>
                    <td>${client.name}</td>
                    <td>${client.group || '-'}</td>
                    <td>${client.is_master ? '<span class="badge bg-primary">主控</span>' : '<span class="badge bg-info">从控</span>'}</td>
                    <td>${statusBadge}</td>
                    <td>${lastOnline}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-info" onclick="alert('详情: ${client.client_id}')" title="查看详情">详情</button>
                            <button class="btn btn-outline-warning" onclick="alert('编辑: ${client.id}')" title="编辑">编辑</button>
                            <button class="btn btn-outline-danger" onclick="alert('删除: ${client.client_id}')" title="删除">删除</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        }

        function testUpdateClientList() {
            updateClientList(testClients);
            document.getElementById('testResult').innerHTML = '<div class="alert alert-success">updateClientList函数执行完成，请查看表格</div>';
        }
    </script>
</body>
</html>
