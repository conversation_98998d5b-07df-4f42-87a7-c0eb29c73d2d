package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"web3-control/internal/models"
	"web3-control/module/client/browser"
	"web3-control/module/client/config"
	"web3-control/module/client/connection"
	"web3-control/module/client/event"
)

// 命令行参数
var (
	configPath   = flag.String("config", "configs/client.yaml", "配置文件路径")
	serverURL    = flag.String("server", "", "服务器WebSocket地址")
	clientID     = flag.String("client-id", "", "客户端ID")
	clientName   = flag.String("name", "", "客户端名称")
	clientGroup  = flag.String("group", "", "客户端分组")
	authToken    = flag.String("auth", "", "认证令牌")
	isMaster     = flag.Bool("is-master", false, "是否主控客户端")
	noiseLevel   = flag.Float64("noise", 0.0, "噪声级别(0-1)")
	chromePath   = flag.String("chrome-path", "", "Chrome可执行文件路径")
	userDataDir  = flag.String("user-data-dir", "", "Chrome用户数据目录")
	debugPort    = flag.Int("debug-port", 0, "Chrome调试端口")
)

// Client 客户端结构
type Client struct {
	config            *config.ClientConfig
	connectionManager *connection.ConnectionManager
	browserManager    browser.BrowserManager
	eventProcessor    event.EventProcessor
	ctx               context.Context
	cancel            context.CancelFunc
}

func main() {
	flag.Parse()

	// 创建客户端
	client, err := NewClient()
	if err != nil {
		log.Fatalf("创建客户端失败: %v", err)
	}

	// 启动客户端
	if err := client.Start(); err != nil {
		log.Fatalf("启动客户端失败: %v", err)
	}

	// 等待中断信号
	client.WaitForShutdown()

	// 停止客户端
	if err := client.Stop(); err != nil {
		log.Printf("停止客户端失败: %v", err)
	}

	log.Println("客户端已退出")
}

// NewClient 创建新的客户端
func NewClient() (*Client, error) {
	// 加载配置
	cfg, err := loadConfig()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %v", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())

	// 创建认证管理器
	authManager := connection.NewAuthManager(
		cfg.Client.ID,
		cfg.Server.AuthToken,
		cfg.Client.Name,
		cfg.Client.Group,
		cfg.Client.IsMaster,
	)

	// 创建连接管理器
	connManager := connection.NewConnectionManager(cfg.Server.URL, authManager)
	connManager.SetReconnectConfig(cfg.Reconnect.MaxRetries, cfg.Reconnect.Interval)

	// 创建浏览器管理器
	browserMgr := browser.NewBrowserManager(cfg.Client.NoiseLevel)

	// 创建事件处理器
	eventProc := event.NewEventProcessor(cfg.Client.NoiseLevel)
	eventProc.SetBrowserManager(browserMgr)

	client := &Client{
		config:            cfg,
		connectionManager: connManager,
		browserManager:    browserMgr,
		eventProcessor:    eventProc,
		ctx:               ctx,
		cancel:            cancel,
	}

	// 注册消息处理器
	client.registerMessageHandlers()

	return client, nil
}

// loadConfig 加载配置
func loadConfig() (*config.ClientConfig, error) {
	// 首先从配置文件加载
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		return nil, err
	}

	// 命令行参数覆盖配置文件
	if *serverURL != "" {
		cfg.Server.URL = *serverURL
	}
	if *clientID != "" {
		cfg.Client.ID = *clientID
	}
	if *clientName != "" {
		cfg.Client.Name = *clientName
	}
	if *clientGroup != "" {
		cfg.Client.Group = *clientGroup
	}
	if *authToken != "" {
		cfg.Server.AuthToken = *authToken
	}
	if *isMaster {
		cfg.Client.IsMaster = *isMaster
	}
	if *noiseLevel > 0 {
		cfg.Client.NoiseLevel = *noiseLevel
	}
	if *chromePath != "" {
		cfg.Chrome.Path = *chromePath
	}
	if *userDataDir != "" {
		cfg.Chrome.UserDataDir = *userDataDir
	}
	if *debugPort > 0 {
		cfg.Chrome.DebugPort = *debugPort
	}

	return cfg, nil
}

// registerMessageHandlers 注册消息处理器
func (c *Client) registerMessageHandlers() {
	// 心跳响应处理器
	c.connectionManager.RegisterMessageHandler("heartbeat", func(msg *models.WebSocketMessage) error {
		if msg.Data == "ping" {
			// 更新最后pong时间
			c.connectionManager.UpdateLastPong()
		}
		return nil
	})

	// 事件处理器
	c.connectionManager.RegisterMessageHandler("event", func(msg *models.WebSocketMessage) error {
		// 解析事件数据
		eventData, ok := msg.Data.(map[string]interface{})
		if !ok {
			return fmt.Errorf("无效的事件数据格式")
		}

		// 转换为Event结构
		eventBytes, err := json.Marshal(eventData)
		if err != nil {
			return fmt.Errorf("序列化事件数据失败: %v", err)
		}

		var event models.Event
		if err := json.Unmarshal(eventBytes, &event); err != nil {
			return fmt.Errorf("解析事件数据失败: %v", err)
		}

		// 处理事件
		return c.eventProcessor.ProcessEvent(&event)
	})

	// 命令处理器
	c.connectionManager.RegisterMessageHandler("command", func(msg *models.WebSocketMessage) error {
		// 解析命令数据
		commandData, ok := msg.Data.(map[string]interface{})
		if !ok {
			return fmt.Errorf("无效的命令数据格式")
		}

		// 处理命令
		return c.eventProcessor.ProcessCommand(commandData)
	})

	// 状态请求处理器
	c.connectionManager.RegisterMessageHandler("status_request", func(msg *models.WebSocketMessage) error {
		return c.handleStatusRequest()
	})
}

// Start 启动客户端
func (c *Client) Start() error {
	log.Printf("正在启动客户端: %s", c.config.Client.Name)

	// 连接到服务器
	if err := c.connectionManager.Connect(); err != nil {
		return fmt.Errorf("连接服务器失败: %v", err)
	}

	// 启动状态上报
	go c.statusReportLoop()

	log.Printf("客户端启动成功: %s", c.connectionManager.GetAuthManager().String())

	return nil
}

// Stop 停止客户端
func (c *Client) Stop() error {
	log.Printf("正在停止客户端...")

	// 取消上下文
	c.cancel()

	// 关闭所有浏览器
	if browserMgr, ok := c.browserManager.(*browser.DefaultBrowserManager); ok {
		browserMgr.Shutdown()
	}

	// 停止事件处理器
	if eventProc, ok := c.eventProcessor.(*event.DefaultEventProcessor); ok {
		eventProc.Stop()
	}

	// 断开连接
	c.connectionManager.Disconnect()

	log.Printf("客户端已停止")

	return nil
}

// WaitForShutdown 等待关闭信号
func (c *Client) WaitForShutdown() {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	select {
	case sig := <-sigChan:
		log.Printf("收到信号: %v", sig)
	case <-c.ctx.Done():
		log.Printf("上下文已取消")
	}
}

// statusReportLoop 状态上报循环
func (c *Client) statusReportLoop() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒上报一次
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.reportStatus()
		}
	}
}

// reportStatus 上报状态
func (c *Client) reportStatus() {
	if !c.connectionManager.IsConnected() {
		return
	}

	// 收集浏览器状态
	browsers := c.browserManager.ListBrowsers()
	browserStatuses := make([]map[string]interface{}, 0, len(browsers))

	for _, browser := range browsers {
		status, err := c.browserManager.GetBrowserStatus(browser.ID)
		if err != nil {
			continue
		}

		browserStatuses = append(browserStatuses, map[string]interface{}{
			"browser_id":    status.BrowserID,
			"status":        status.Status,
			"url":           status.URL,
			"memory_usage":  status.MemoryUsage,
			"cpu_usage":     status.CPUUsage,
			"updated_at":    status.UpdatedAt,
		})
	}

	// 创建状态消息
	statusMsg := &models.WebSocketMessage{
		Type:      "status",
		Timestamp: time.Now().Unix(),
		Data: map[string]interface{}{
			"client_id": c.connectionManager.GetAuthManager().GetClientID(),
			"browsers":  browserStatuses,
		},
	}

	// 发送状态消息
	if err := c.connectionManager.SendMessage(statusMsg); err != nil {
		log.Printf("发送状态消息失败: %v", err)
	}
}

// handleStatusRequest 处理状态请求
func (c *Client) handleStatusRequest() error {
	c.reportStatus()
	return nil
}