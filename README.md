# 浏览器群控系统

这是一个基于 Chrome DevTools Protocol (CDP) 的浏览器群控系统，支持一个主控浏览器的操作同步到多个从控浏览器，并具备事件录制、重放和安全随机化功能。

## 功能特点

- **主控同步**: 在主控浏览器中的操作（点击、输入、滚动）实时同步到从控浏览器
- **事件录制**: 记录主控浏览器的操作序列，保存为可重复使用的脚本
- **事件重放**: 将录制的操作序列重放到一个或多个从控浏览器
- **安全随机化**: 为每个从控浏览器添加随机噪声，模拟真实人类行为
- **多客户端**：支持跨设备客户端
- **分组控制**: 支持将设备分组，针对特定组执行操作
- **Web控制台**: 提供直观的Web界面管理设备和录制

## 系统架构

- **服务端**: 使用 Golang + gin + gorm + WebSocket 实现，负责管理客户端连接、事件广播和录制管理
- **客户端**: 使用 Golang + CDP 实现，负责控制本地 Chrome 浏览器
- **主控注入脚本**: JavaScript 脚本注入到主控浏览器，监听用户操作并发送到服务端

## 根目录结构
```
web3-control/
├── cmd/                  # 主程序入口
│   ├── server/           # 服务端程序
│   └── client/           # 客户端程序
├── module/               # 模块目录
│   ├── server/           # 服务端模块
│   │   ├── handler/      # HTTP处理器
│   │   ├── model/        # 数据模型
│   │   ├── service/      # 业务逻辑
│   │   └── config/       # 服务端配置
│   └── client/           # 客户端模块
│       ├── browser/      # 浏览器控制
│       ├── event/        # 事件处理
│       └── noise/        # 随机化处理
├── internal/             # 内部公共库
│   ├── protocol/         # 协议定义
│   ├── utils/            # 工具函数
│   └── common/           # 公共组件
├── web/                  # Web控制台
│   ├── static/           # 静态资源
│   └── template/         # 模板文件
├── configs/              # 配置文件
├── scripts/              # 脚本文件
├── docs/                 # 文档
├── test/                 # 测试文件
├── go.mod                # Go模块文件
├── go.sum                # Go依赖版本锁定文件
├── web3.sql              # 数据库文件
└── README.md             # 项目说明文档
```

## 安装与使用

### 前置条件

- Go 1.23+
- Chrome 浏览器

### 安装依赖

```bash
go get github.com/gin-gonic/gin
go get github.com/gorilla/websocket
go get github.com/chromedp/chromedp
```

### 启动服务端

```bash
cd cmd/server
go build
./bin/server --addr=:8080 --auth=your-secret-key
```

服务端参数:
- `--addr`: 服务器监听地址，默认 `:8080`
- `--auth`: 认证密钥，默认 `secret-key`

### 启动客户端

#### 编译客户端

```bash
cd cmd/client
go build -o ../../client.exe

# 交叉编译
GOOS=linux GOARCH=amd64 go build -o ../../client-linux
GOOS=windows GOARCH=amd64 go build -o ../../client.exe
```

#### 主控客户端

```bash
./client.exe --server=ws://localhost:8080/ws --name="主控客户端" --auth=your-secret-key --is-master=true
```

#### 从控客户端

```bash
./client.exe --server=ws://localhost:8080/ws --name="从控客户端1" --group=group1 --auth=your-secret-key --noise=0.3
```

#### 使用配置文件启动

```bash
./client.exe --config=configs/client.yaml
```

#### 客户端参数说明

- `--server`: 服务器WebSocket地址
- `--client-id`: 客户端ID，如未指定则自动生成
- `--name`: 客户端名称，如未指定则自动生成
- `--group`: 客户端分组，用于分组控制
- `--auth`: 认证密钥，必须与服务端一致
- `--is-master`: 是否为主控客户端，默认 `false`
- `--config`: 配置文件路径，默认 `configs/client.yaml`
- `--noise`: 噪声级别 (0-1)，默认 `0.2`
- `--chrome-path`: Chrome可执行文件路径，如未指定则自动查找
- `--user-data-dir`: Chrome用户数据目录，如未指定则使用临时目录
- `--debug-port`: Chrome调试端口，默认 `9222`

#### 客户端配置文件示例 (configs/client.yaml)

```yaml
server:
  url: "ws://localhost:8080/ws"
  auth_token: "secret-key"

client:
  id: ""  # 自动生成
  name: "客户端-1"
  group: "default"
  is_master: false
  noise_level: 0.2

chrome:
  path: ""  # 自动检测
  user_data_dir: ""  # 临时目录
  debug_port: 9222
  headless: false
  disable_gpu: false
  additional_args: []

reconnect:
  max_retries: 10
  interval: "5s"

logging:
  level: "info"
  file: "logs/client.log"
```

### 使用Web控制台

1. 打开浏览器访问 `http://localhost:8080`
2. 在控制台中可以:
   - 管理所有的客户端
   - 开始/停止录制
   - 查看录制列表
   - 选择录制进行重放
   - 指定目标分组进行重放
   - 管理所有浏览器，配置绑定浏览器的信息，例如代理、debug-port、user-data-dir
   - 管理所有代理

## 使用场景

- **多账号管理**: 同时操作多个社交媒体、电商账号
- **自动化测试**: 在多个浏览器环境中同时测试网站功能
- **数据采集**: 批量采集网页数据，添加随机性避免被检测
- **营销自动化**: 批量执行营销操作，如点赞、评论、关注等

## 安全性考虑

- 使用认证密钥保护服务端连接
- 每个从控客户端添加随机噪声，避免被检测为自动化工具
- 支持模拟真实人类行为，如鼠标轨迹、打字速度等

## 扩展开发

### 自定义事件处理

可以在 `client/main.go` 中的 `handleEvent` 函数中添加自定义事件处理逻辑。

### 添加新的噪声类型

可以在 `client/noise.go` 中添加新的噪声处理函数，并在 `NoiseConfig` 结构体中添加相应配置。

### 自定义录制格式

可以在 `server/recorder.go` 中修改 `RecordingSession` 结构体和相关函数，以支持自定义录制格式。