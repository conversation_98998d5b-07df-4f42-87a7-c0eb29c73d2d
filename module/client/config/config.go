package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v3"
)

// ClientConfig 客户端配置结构体
type ClientConfig struct {
	Server struct {
		URL       string `yaml:"url"`        // 服务器WebSocket地址
		AuthToken string `yaml:"auth_token"` // 认证令牌
	} `yaml:"server"`

	Client struct {
		ID         string  `yaml:"id"`          // 客户端ID
		Name       string  `yaml:"name"`        // 客户端名称
		Group      string  `yaml:"group"`       // 分组
		IsMaster   bool    `yaml:"is_master"`   // 是否主控
		NoiseLevel float64 `yaml:"noise_level"` // 噪声级别
	} `yaml:"client"`

	Chrome struct {
		Path           string   `yaml:"path"`             // Chrome可执行文件路径
		UserDataDir    string   `yaml:"user_data_dir"`    // 用户数据目录
		DebugPort      int      `yaml:"debug_port"`       // 调试端口
		Headless       bool     `yaml:"headless"`         // 是否无头模式
		DisableGPU     bool     `yaml:"disable_gpu"`      // 是否禁用GPU
		AdditionalArgs []string `yaml:"additional_args"`  // 额外启动参数
	} `yaml:"chrome"`

	Reconnect struct {
		MaxRetries int           `yaml:"max_retries"` // 最大重试次数
		Interval   time.Duration `yaml:"interval"`    // 重试间隔
	} `yaml:"reconnect"`

	Logging struct {
		Level string `yaml:"level"` // 日志级别
		File  string `yaml:"file"`  // 日志文件路径
	} `yaml:"logging"`
}

// LoadConfig 从文件加载配置
func LoadConfig(path string) (*ClientConfig, error) {
	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(path); os.IsNotExist(err) {
		defaultConfig := GetDefaultConfig()
		if err := SaveConfig(path, defaultConfig); err != nil {
			return nil, fmt.Errorf("创建默认配置文件失败: %v", err)
		}
		return defaultConfig, nil
	}

	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config ClientConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 设置默认值
	setDefaults(&config)

	return &config, nil
}

// SaveConfig 保存配置到文件
func SaveConfig(path string, config *ClientConfig) error {
	// 确保目录存在
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %v", err)
	}

	data, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	if err := os.WriteFile(path, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	return nil
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *ClientConfig {
	config := &ClientConfig{}

	// 服务器配置
	config.Server.URL = "ws://localhost:8080/ws"
	config.Server.AuthToken = "secret-key"

	// 客户端配置
	config.Client.ID = ""  // 自动生成
	config.Client.Name = "客户端-1"
	config.Client.Group = "default"
	config.Client.IsMaster = false
	config.Client.NoiseLevel = 0.2

	// Chrome配置
	config.Chrome.Path = ""  // 自动检测
	config.Chrome.UserDataDir = ""  // 临时目录
	config.Chrome.DebugPort = 9222
	config.Chrome.Headless = false
	config.Chrome.DisableGPU = false
	config.Chrome.AdditionalArgs = []string{}

	// 重连配置
	config.Reconnect.MaxRetries = 10
	config.Reconnect.Interval = 5 * time.Second

	// 日志配置
	config.Logging.Level = "info"
	config.Logging.File = "logs/client.log"

	return config
}

// setDefaults 设置默认值
func setDefaults(config *ClientConfig) {
	if config.Server.URL == "" {
		config.Server.URL = "ws://localhost:8080/ws"
	}
	if config.Server.AuthToken == "" {
		config.Server.AuthToken = "secret-key"
	}
	if config.Client.Name == "" {
		config.Client.Name = "客户端-1"
	}
	if config.Client.Group == "" {
		config.Client.Group = "default"
	}
	if config.Client.NoiseLevel == 0 {
		config.Client.NoiseLevel = 0.2
	}
	if config.Chrome.DebugPort == 0 {
		config.Chrome.DebugPort = 9222
	}
	if config.Reconnect.MaxRetries == 0 {
		config.Reconnect.MaxRetries = 10
	}
	if config.Reconnect.Interval == 0 {
		config.Reconnect.Interval = 5 * time.Second
	}
	if config.Logging.Level == "" {
		config.Logging.Level = "info"
	}
	if config.Logging.File == "" {
		config.Logging.File = "logs/client.log"
	}
}

// Validate 验证配置
func (c *ClientConfig) Validate() error {
	if c.Server.URL == "" {
		return fmt.Errorf("服务器URL不能为空")
	}
	if c.Server.AuthToken == "" {
		return fmt.Errorf("认证令牌不能为空")
	}
	if c.Client.Name == "" {
		return fmt.Errorf("客户端名称不能为空")
	}
	if c.Client.NoiseLevel < 0 || c.Client.NoiseLevel > 1 {
		return fmt.Errorf("噪声级别必须在0-1之间")
	}
	if c.Chrome.DebugPort <= 0 || c.Chrome.DebugPort > 65535 {
		return fmt.Errorf("Chrome调试端口必须在1-65535之间")
	}
	if c.Reconnect.MaxRetries < 0 {
		return fmt.Errorf("最大重试次数不能为负数")
	}
	if c.Reconnect.Interval < 0 {
		return fmt.Errorf("重试间隔不能为负数")
	}
	return nil
}
