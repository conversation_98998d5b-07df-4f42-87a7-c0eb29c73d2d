package event

import (
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"web3-control/internal/models"
	"web3-control/module/client/browser"
	"web3-control/module/client/noise"
)

// EventProcessor 事件处理器接口
type EventProcessor interface {
	ProcessEvent(event *models.Event) error
	ProcessCommand(command map[string]interface{}) error
	SetBrowserManager(manager browser.BrowserManager)
	SetNoiseLevel(level float64)
}

// DefaultEventProcessor 默认事件处理器实现
type DefaultEventProcessor struct {
	browserManager browser.BrowserManager
	noiseGenerator *noise.NoiseGenerator
	humanizer      *noise.Humanizer
	executor       *EventExecutor
	eventQueue     chan *EventTask
	isProcessing   bool
	mutex          sync.RWMutex
}

// EventTask 事件任务
type EventTask struct {
	Event     *models.Event
	BrowserID string
	Callback  func(error)
}

// NewEventProcessor 创建事件处理器
func NewEventProcessor(noiseLevel float64) EventProcessor {
	processor := &DefaultEventProcessor{
		noiseGenerator: noise.NewNoiseGenerator(noiseLevel),
		humanizer:      noise.NewHumanizer(noiseLevel),
		eventQueue:     make(chan *EventTask, 1000),
		isProcessing:   false,
	}

	processor.executor = NewEventExecutor(processor.noiseGenerator, processor.humanizer)

	// 启动事件处理循环
	go processor.processEventLoop()

	return processor
}

// SetBrowserManager 设置浏览器管理器
func (ep *DefaultEventProcessor) SetBrowserManager(manager browser.BrowserManager) {
	ep.mutex.Lock()
	defer ep.mutex.Unlock()

	ep.browserManager = manager
	if ep.executor != nil {
		ep.executor.SetBrowserManager(manager)
	}
}

// SetNoiseLevel 设置噪声级别
func (ep *DefaultEventProcessor) SetNoiseLevel(level float64) {
	ep.mutex.Lock()
	defer ep.mutex.Unlock()

	ep.noiseGenerator.SetLevel(level)
	ep.humanizer.SetNoiseLevel(level)
	if ep.executor != nil {
		ep.executor.SetNoiseLevel(level)
	}
}

// ProcessEvent 处理事件
func (ep *DefaultEventProcessor) ProcessEvent(event *models.Event) error {
	if ep.browserManager == nil {
		return fmt.Errorf("浏览器管理器未设置")
	}

	// 获取所有运行中的浏览器
	browsers := ep.browserManager.GetRunningBrowsers()
	if len(browsers) == 0 {
		log.Printf("没有运行中的浏览器，跳过事件处理")
		return nil
	}

	// 为每个浏览器创建事件任务
	for _, browserInstance := range browsers {
		// 为事件添加噪声
		noisyEvent := ep.noiseGenerator.AddNoise(event)

		// 创建事件任务
		task := &EventTask{
			Event:     noisyEvent,
			BrowserID: browserInstance.ID,
			Callback: func(err error) {
				if err != nil {
					log.Printf("事件执行失败 [浏览器:%s]: %v", browserInstance.ID, err)
				}
			},
		}

		// 将任务加入队列
		select {
		case ep.eventQueue <- task:
			// 成功加入队列
		default:
			log.Printf("事件队列已满，丢弃事件")
		}
	}

	return nil
}

// ProcessCommand 处理命令
func (ep *DefaultEventProcessor) ProcessCommand(command map[string]interface{}) error {
	cmdType, ok := command["command"].(string)
	if !ok {
		return fmt.Errorf("无效的命令格式")
	}

	switch cmdType {
	case "launch_browser":
		return ep.processLaunchBrowserCommand(command)
	case "close_browser":
		return ep.processCloseBrowserCommand(command)
	case "execute_event":
		return ep.processExecuteEventCommand(command)
	case "navigate":
		return ep.processNavigateCommand(command)
	default:
		return fmt.Errorf("不支持的命令类型: %s", cmdType)
	}
}

// processLaunchBrowserCommand 处理启动浏览器命令
func (ep *DefaultEventProcessor) processLaunchBrowserCommand(command map[string]interface{}) error {
	if ep.browserManager == nil {
		return fmt.Errorf("浏览器管理器未设置")
	}

	// 解析浏览器配置
	configData, ok := command["config"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("缺少浏览器配置")
	}

	// 转换为BrowserConfig
	configBytes, err := json.Marshal(configData)
	if err != nil {
		return fmt.Errorf("序列化浏览器配置失败: %v", err)
	}

	var config models.BrowserConfig
	if err := json.Unmarshal(configBytes, &config); err != nil {
		return fmt.Errorf("解析浏览器配置失败: %v", err)
	}

	// 启动浏览器
	_, err = ep.browserManager.LaunchBrowser(&config)
	if err != nil {
		return fmt.Errorf("启动浏览器失败: %v", err)
	}

	log.Printf("成功启动浏览器: %s", config.Name)
	return nil
}

// processCloseBrowserCommand 处理关闭浏览器命令
func (ep *DefaultEventProcessor) processCloseBrowserCommand(command map[string]interface{}) error {
	if ep.browserManager == nil {
		return fmt.Errorf("浏览器管理器未设置")
	}

	browserID, ok := command["browser_id"].(string)
	if !ok {
		return fmt.Errorf("缺少浏览器ID")
	}

	// 关闭浏览器
	err := ep.browserManager.CloseBrowser(browserID)
	if err != nil {
		return fmt.Errorf("关闭浏览器失败: %v", err)
	}

	log.Printf("成功关闭浏览器: %s", browserID)
	return nil
}

// processExecuteEventCommand 处理执行事件命令
func (ep *DefaultEventProcessor) processExecuteEventCommand(command map[string]interface{}) error {
	// 解析事件数据
	eventData, ok := command["event"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("缺少事件数据")
	}

	// 转换为Event
	eventBytes, err := json.Marshal(eventData)
	if err != nil {
		return fmt.Errorf("序列化事件数据失败: %v", err)
	}

	var event models.Event
	if err := json.Unmarshal(eventBytes, &event); err != nil {
		return fmt.Errorf("解析事件数据失败: %v", err)
	}

	// 处理事件
	return ep.ProcessEvent(&event)
}

// processNavigateCommand 处理导航命令
func (ep *DefaultEventProcessor) processNavigateCommand(command map[string]interface{}) error {
	url, ok := command["url"].(string)
	if !ok {
		return fmt.Errorf("缺少导航URL")
	}

	// 创建导航事件
	event := &models.Event{
		Type: "navigate",
		Data: map[string]interface{}{
			"url": url,
		},
	}

	// 处理事件
	return ep.ProcessEvent(event)
}

// processEventLoop 事件处理循环
func (ep *DefaultEventProcessor) processEventLoop() {
	ep.mutex.Lock()
	ep.isProcessing = true
	ep.mutex.Unlock()

	defer func() {
		ep.mutex.Lock()
		ep.isProcessing = false
		ep.mutex.Unlock()
	}()

	for task := range ep.eventQueue {
		// 执行事件
		err := ep.executor.ExecuteEvent(task.BrowserID, task.Event)
		
		// 调用回调函数
		if task.Callback != nil {
			task.Callback(err)
		}

		// 添加事件间隔，避免操作过于频繁
		time.Sleep(50 * time.Millisecond)
	}
}

// GetQueueSize 获取队列大小
func (ep *DefaultEventProcessor) GetQueueSize() int {
	return len(ep.eventQueue)
}

// IsProcessing 检查是否正在处理事件
func (ep *DefaultEventProcessor) IsProcessing() bool {
	ep.mutex.RLock()
	defer ep.mutex.RUnlock()
	
	return ep.isProcessing
}

// Stop 停止事件处理器
func (ep *DefaultEventProcessor) Stop() {
	close(ep.eventQueue)
}

// GetStats 获取处理器统计信息
func (ep *DefaultEventProcessor) GetStats() map[string]interface{} {
	ep.mutex.RLock()
	defer ep.mutex.RUnlock()

	return map[string]interface{}{
		"queue_size":    len(ep.eventQueue),
		"is_processing": ep.isProcessing,
		"noise_level":   ep.noiseGenerator.GetLevel(),
	}
}
