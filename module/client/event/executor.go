package event

import (
	"fmt"
	"log"
	"sync"
	"time"

	"web3-control/internal/models"
	"web3-control/module/client/browser"
	"web3-control/module/client/noise"
)

// EventExecutor 事件执行器
type EventExecutor struct {
	browserManager browser.BrowserManager
	controller     *browser.BrowserController
	noiseGenerator *noise.NoiseGenerator
	humanizer      *noise.Humanizer
	mutex          sync.RWMutex
	
	// 执行统计
	totalExecuted int
	successCount  int
	errorCount    int
}

// NewEventExecutor 创建事件执行器
func NewEventExecutor(noiseGen *noise.NoiseGenerator, humanizer *noise.Humanizer) *EventExecutor {
	return &EventExecutor{
		noiseGenerator: noiseGen,
		humanizer:      humanizer,
		totalExecuted:  0,
		successCount:   0,
		errorCount:     0,
	}
}

// SetBrowserManager 设置浏览器管理器
func (ee *EventExecutor) SetBrowserManager(manager browser.BrowserManager) {
	ee.mutex.Lock()
	defer ee.mutex.Unlock()

	ee.browserManager = manager
	ee.controller = browser.NewBrowserController(manager)
}

// SetNoiseLevel 设置噪声级别
func (ee *EventExecutor) SetNoiseLevel(level float64) {
	ee.mutex.Lock()
	defer ee.mutex.Unlock()

	ee.noiseGenerator.SetLevel(level)
	ee.humanizer.SetNoiseLevel(level)
}

// ExecuteEvent 执行事件
func (ee *EventExecutor) ExecuteEvent(browserID string, event *models.Event) error {
	ee.mutex.Lock()
	ee.totalExecuted++
	ee.mutex.Unlock()

	if ee.controller == nil {
		ee.incrementErrorCount()
		return fmt.Errorf("浏览器控制器未初始化")
	}

	// 记录开始时间
	startTime := time.Now()

	// 根据事件类型进行特殊处理
	var err error
	switch event.Type {
	case "input":
		err = ee.executeInputEvent(browserID, event)
	case "click", "dblclick":
		err = ee.executeClickEvent(browserID, event)
	case "scroll":
		err = ee.executeScrollEvent(browserID, event)
	default:
		err = ee.controller.ExecuteEvent(browserID, event)
	}

	// 记录执行结果
	duration := time.Since(startTime)
	if err != nil {
		ee.incrementErrorCount()
		log.Printf("事件执行失败 [%s] 浏览器:%s 耗时:%v 错误:%v", 
			event.Type, browserID, duration, err)
	} else {
		ee.incrementSuccessCount()
		log.Printf("事件执行成功 [%s] 浏览器:%s 耗时:%v", 
			event.Type, browserID, duration)
	}

	return err
}

// executeInputEvent 执行输入事件（支持人类化打字）
func (ee *EventExecutor) executeInputEvent(browserID string, event *models.Event) error {
	value, ok := event.Data["value"].(string)
	if !ok {
		return ee.controller.ExecuteEvent(browserID, event)
	}

	// 如果噪声级别较高，使用人类化打字
	if ee.noiseGenerator.GetLevel() > 0.3 {
		return ee.executeHumanizedTyping(browserID, event, value)
	}

	// 否则直接执行
	return ee.controller.ExecuteEvent(browserID, event)
}

// executeHumanizedTyping 执行人类化打字
func (ee *EventExecutor) executeHumanizedTyping(browserID string, event *models.Event, text string) error {
	selector, ok := event.Data["selector"].(string)
	if !ok {
		return fmt.Errorf("缺少选择器")
	}

	// 生成打字模式
	typingEvents := ee.humanizer.SimulateTypingPattern(text)

	// 先聚焦到输入框
	focusEvent := &models.Event{
		Type: "focus",
		Data: map[string]interface{}{
			"selector": selector,
		},
	}

	if err := ee.controller.ExecuteEvent(browserID, focusEvent); err != nil {
		return fmt.Errorf("聚焦输入框失败: %v", err)
	}

	// 如果需要清空，先清空
	if clear, _ := event.Data["clear"].(bool); clear {
		clearEvent := &models.Event{
			Type: "keypress",
			Data: map[string]interface{}{
				"key":     "a",
				"code":    "KeyA",
				"ctrlKey": true,
			},
		}
		ee.controller.ExecuteEvent(browserID, clearEvent)

		deleteEvent := &models.Event{
			Type: "keypress",
			Data: map[string]interface{}{
				"key":  "Delete",
				"code": "Delete",
			},
		}
		ee.controller.ExecuteEvent(browserID, deleteEvent)
	}

	// 逐个执行打字事件
	for _, typingEvent := range typingEvents {
		if err := ee.controller.ExecuteEvent(browserID, &typingEvent); err != nil {
			log.Printf("打字事件执行失败: %v", err)
			// 继续执行其他字符
		}
	}

	return nil
}

// executeClickEvent 执行点击事件（支持鼠标轨迹）
func (ee *EventExecutor) executeClickEvent(browserID string, event *models.Event) error {
	// 如果有坐标信息且噪声级别较高，生成鼠标轨迹
	if x, xOk := event.Data["x"].(float64); xOk {
		if y, yOk := event.Data["y"].(float64); yOk && ee.noiseGenerator.GetLevel() > 0.4 {
			return ee.executeClickWithTrajectory(browserID, event, x, y)
		}
	}

	// 否则直接执行
	return ee.controller.ExecuteEvent(browserID, event)
}

// executeClickWithTrajectory 执行带轨迹的点击
func (ee *EventExecutor) executeClickWithTrajectory(browserID string, event *models.Event, targetX, targetY float64) error {
	// 生成鼠标轨迹
	from := noise.Point{X: 0, Y: 0} // 可以从上次位置开始
	to := noise.Point{X: targetX, Y: targetY}
	trajectory := ee.humanizer.GenerateMouseTrajectory(from, to)

	// 执行鼠标移动轨迹
	for i, point := range trajectory {
		if i == len(trajectory)-1 {
			// 最后一个点执行点击
			clickEvent := &models.Event{
				Type: event.Type,
				Data: map[string]interface{}{
					"x": point.X,
					"y": point.Y,
				},
			}
			return ee.controller.ExecuteEvent(browserID, clickEvent)
		} else {
			// 中间点执行鼠标移动
			moveEvent := &models.Event{
				Type: "mousemove",
				Data: map[string]interface{}{
					"x": point.X,
					"y": point.Y,
				},
				Delay: 10, // 10ms间隔
			}
			ee.controller.ExecuteEvent(browserID, moveEvent)
		}
	}

	return nil
}

// executeScrollEvent 执行滚动事件（支持平滑滚动）
func (ee *EventExecutor) executeScrollEvent(browserID string, event *models.Event) error {
	deltaY, ok := event.Data["deltaY"].(float64)
	if !ok || ee.noiseGenerator.GetLevel() < 0.3 {
		// 直接执行
		return ee.controller.ExecuteEvent(browserID, event)
	}

	// 生成平滑滚动
	scrollEvents := ee.humanizer.SimulateScrollBehavior(deltaY)

	// 执行滚动序列
	for _, scrollEvent := range scrollEvents {
		if err := ee.controller.ExecuteEvent(browserID, &scrollEvent); err != nil {
			log.Printf("滚动事件执行失败: %v", err)
		}
	}

	return nil
}

// ExecuteBatch 批量执行事件
func (ee *EventExecutor) ExecuteBatch(browserID string, events []*models.Event) error {
	var errors []error

	for i, event := range events {
		if err := ee.ExecuteEvent(browserID, event); err != nil {
			errors = append(errors, fmt.Errorf("事件%d执行失败: %v", i, err))
		}

		// 添加事件间隔
		if i < len(events)-1 {
			pause := ee.humanizer.SimulateHumanPause()
			time.Sleep(pause)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("批量执行失败: %v", errors)
	}

	return nil
}

// incrementSuccessCount 增加成功计数
func (ee *EventExecutor) incrementSuccessCount() {
	ee.mutex.Lock()
	defer ee.mutex.Unlock()
	ee.successCount++
}

// incrementErrorCount 增加错误计数
func (ee *EventExecutor) incrementErrorCount() {
	ee.mutex.Lock()
	defer ee.mutex.Unlock()
	ee.errorCount++
}

// GetStats 获取执行统计
func (ee *EventExecutor) GetStats() map[string]interface{} {
	ee.mutex.RLock()
	defer ee.mutex.RUnlock()

	successRate := 0.0
	if ee.totalExecuted > 0 {
		successRate = float64(ee.successCount) / float64(ee.totalExecuted) * 100
	}

	return map[string]interface{}{
		"total_executed": ee.totalExecuted,
		"success_count":  ee.successCount,
		"error_count":    ee.errorCount,
		"success_rate":   successRate,
		"noise_level":    ee.noiseGenerator.GetLevel(),
	}
}

// ResetStats 重置统计
func (ee *EventExecutor) ResetStats() {
	ee.mutex.Lock()
	defer ee.mutex.Unlock()

	ee.totalExecuted = 0
	ee.successCount = 0
	ee.errorCount = 0
}
