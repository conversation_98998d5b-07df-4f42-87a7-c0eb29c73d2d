package noise

import (
	"math"
	"math/rand"
	"time"

	"web3-control/internal/models"
)

// NoiseConfig 噪声配置
type NoiseConfig struct {
	Level            float64 // 噪声级别 (0-1)
	DelayRange       int     // 延迟范围 (毫秒)
	PositionOffset   int     // 位置偏移范围 (像素)
	TypingSpeedRange int     // 打字速度范围 (毫秒)
	ScrollOffset     int     // 滚动偏移范围 (像素)
}

// NoiseGenerator 噪声生成器
type NoiseGenerator struct {
	config *NoiseConfig
	rng    *rand.Rand
}

// NewNoiseGenerator 创建噪声生成器
func NewNoiseGenerator(level float64) *NoiseGenerator {
	config := &NoiseConfig{
		Level:            level,
		DelayRange:       int(level * 1000),      // 0-1000ms
		PositionOffset:   int(level * 10),        // 0-10px
		TypingSpeedRange: int(level * 200),       // 0-200ms
		ScrollOffset:     int(level * 50),        // 0-50px
	}

	return &NoiseGenerator{
		config: config,
		rng:    rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// SetLevel 设置噪声级别
func (ng *NoiseGenerator) SetLevel(level float64) {
	if level < 0 {
		level = 0
	}
	if level > 1 {
		level = 1
	}

	ng.config.Level = level
	ng.config.DelayRange = int(level * 1000)
	ng.config.PositionOffset = int(level * 10)
	ng.config.TypingSpeedRange = int(level * 200)
	ng.config.ScrollOffset = int(level * 50)
}

// GetLevel 获取噪声级别
func (ng *NoiseGenerator) GetLevel() float64 {
	return ng.config.Level
}

// AddNoise 为事件添加噪声
func (ng *NoiseGenerator) AddNoise(event *models.Event) *models.Event {
	if ng.config.Level == 0 {
		return event
	}

	// 复制事件
	noisyEvent := &models.Event{
		Type:      event.Type,
		Timestamp: event.Timestamp,
		Delay:     event.Delay,
		Data:      make(map[string]interface{}),
	}

	// 复制数据
	for k, v := range event.Data {
		noisyEvent.Data[k] = v
	}

	// 根据事件类型添加噪声
	switch event.Type {
	case "click", "dblclick":
		ng.addClickNoise(noisyEvent)
	case "input":
		ng.addInputNoise(noisyEvent)
	case "scroll":
		ng.addScrollNoise(noisyEvent)
	case "keypress", "keydown", "keyup":
		ng.addKeyNoise(noisyEvent)
	case "mousemove":
		ng.addMouseMoveNoise(noisyEvent)
	}

	// 添加通用延迟噪声
	ng.addDelayNoise(noisyEvent)

	return noisyEvent
}

// addClickNoise 添加点击噪声
func (ng *NoiseGenerator) addClickNoise(event *models.Event) {
	if x, ok := event.Data["x"].(float64); ok {
		offset := ng.generatePositionOffset()
		event.Data["x"] = x + offset
	}

	if y, ok := event.Data["y"].(float64); ok {
		offset := ng.generatePositionOffset()
		event.Data["y"] = y + offset
	}
}

// addInputNoise 添加输入噪声
func (ng *NoiseGenerator) addInputNoise(event *models.Event) {
	// 为输入事件添加打字延迟
	typingDelay := ng.generateTypingDelay()
	if typingDelay > 0 {
		event.Delay += typingDelay
	}
}

// addScrollNoise 添加滚动噪声
func (ng *NoiseGenerator) addScrollNoise(event *models.Event) {
	if deltaY, ok := event.Data["deltaY"].(float64); ok {
		offset := ng.generateScrollOffset()
		event.Data["deltaY"] = deltaY + offset
	}

	if deltaX, ok := event.Data["deltaX"].(float64); ok {
		offset := ng.generateScrollOffset()
		event.Data["deltaX"] = deltaX + offset
	}
}

// addKeyNoise 添加按键噪声
func (ng *NoiseGenerator) addKeyNoise(event *models.Event) {
	// 为按键事件添加延迟
	keyDelay := ng.generateKeyDelay()
	if keyDelay > 0 {
		event.Delay += keyDelay
	}
}

// addMouseMoveNoise 添加鼠标移动噪声
func (ng *NoiseGenerator) addMouseMoveNoise(event *models.Event) {
	if x, ok := event.Data["x"].(float64); ok {
		offset := ng.generatePositionOffset() / 2 // 鼠标移动的偏移较小
		event.Data["x"] = x + offset
	}

	if y, ok := event.Data["y"].(float64); ok {
		offset := ng.generatePositionOffset() / 2
		event.Data["y"] = y + offset
	}
}

// addDelayNoise 添加延迟噪声
func (ng *NoiseGenerator) addDelayNoise(event *models.Event) {
	delay := ng.generateDelay()
	event.Delay += delay
}

// generateDelay 生成随机延迟
func (ng *NoiseGenerator) generateDelay() int {
	if ng.config.DelayRange == 0 {
		return 0
	}
	return ng.rng.Intn(ng.config.DelayRange)
}

// generatePositionOffset 生成位置偏移
func (ng *NoiseGenerator) generatePositionOffset() float64 {
	if ng.config.PositionOffset == 0 {
		return 0
	}
	offset := ng.rng.Intn(ng.config.PositionOffset*2) - ng.config.PositionOffset
	return float64(offset)
}

// generateTypingDelay 生成打字延迟
func (ng *NoiseGenerator) generateTypingDelay() int {
	if ng.config.TypingSpeedRange == 0 {
		return 0
	}
	return ng.rng.Intn(ng.config.TypingSpeedRange)
}

// generateScrollOffset 生成滚动偏移
func (ng *NoiseGenerator) generateScrollOffset() float64 {
	if ng.config.ScrollOffset == 0 {
		return 0
	}
	offset := ng.rng.Intn(ng.config.ScrollOffset*2) - ng.config.ScrollOffset
	return float64(offset)
}

// generateKeyDelay 生成按键延迟
func (ng *NoiseGenerator) generateKeyDelay() int {
	if ng.config.TypingSpeedRange == 0 {
		return 0
	}
	// 按键延迟通常比打字延迟短
	return ng.rng.Intn(ng.config.TypingSpeedRange / 2)
}

// GenerateRandomUserAgent 生成随机User-Agent
func (ng *NoiseGenerator) GenerateRandomUserAgent() string {
	userAgents := []string{
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
		"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
	}

	return userAgents[ng.rng.Intn(len(userAgents))]
}

// GenerateRandomWindowSize 生成随机窗口大小
func (ng *NoiseGenerator) GenerateRandomWindowSize() (int, int) {
	sizes := [][]int{
		{1920, 1080},
		{1366, 768},
		{1440, 900},
		{1536, 864},
		{1280, 720},
		{1600, 900},
	}

	size := sizes[ng.rng.Intn(len(sizes))]
	
	// 添加小幅随机偏移
	width := size[0] + ng.rng.Intn(21) - 10  // ±10px
	height := size[1] + ng.rng.Intn(21) - 10 // ±10px
	
	return width, height
}

// GenerateRandomDelay 生成随机延迟（用于人类行为模拟）
func (ng *NoiseGenerator) GenerateRandomDelay(baseDelay, variance int) time.Duration {
	if variance == 0 {
		return time.Duration(baseDelay) * time.Millisecond
	}
	
	offset := ng.rng.Intn(variance*2) - variance
	delay := baseDelay + offset
	
	if delay < 0 {
		delay = 0
	}
	
	return time.Duration(delay) * time.Millisecond
}

// SimulateHumanPause 模拟人类停顿
func (ng *NoiseGenerator) SimulateHumanPause() time.Duration {
	// 模拟人类思考时间：100ms-2000ms
	baseDelay := 100 + ng.rng.Intn(1900)
	
	// 根据噪声级别调整
	delay := int(float64(baseDelay) * ng.config.Level)
	
	return time.Duration(delay) * time.Millisecond
}
