package noise

import (
	"math"
	"math/rand"
	"time"

	"web3-control/internal/models"
)

// Point 表示一个坐标点
type Point struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

// Humanizer 人类行为模拟器
type Humanizer struct {
	rng         *rand.Rand
	noiseLevel  float64
	lastMousePos Point
}

// NewHumanizer 创建人类行为模拟器
func NewHumanizer(noiseLevel float64) *Humanizer {
	return &Humanizer{
		rng:        rand.New(rand.NewSource(time.Now().UnixNano())),
		noiseLevel: noiseLevel,
		lastMousePos: Point{X: 0, Y: 0},
	}
}

// SetNoiseLevel 设置噪声级别
func (h *Humanizer) SetNoiseLevel(level float64) {
	if level < 0 {
		level = 0
	}
	if level > 1 {
		level = 1
	}
	h.noiseLevel = level
}

// GenerateMouseTrajectory 生成鼠标轨迹
func (h *Humanizer) GenerateMouseTrajectory(from, to Point) []Point {
	if h.noiseLevel == 0 {
		return []Point{from, to}
	}

	distance := h.calculateDistance(from, to)
	
	// 根据距离决定轨迹点数量
	numPoints := int(distance/50) + 2 // 每50像素一个点
	if numPoints > 20 {
		numPoints = 20 // 最多20个点
	}
	if numPoints < 3 {
		numPoints = 3 // 最少3个点
	}

	// 生成贝塞尔曲线轨迹
	trajectory := h.generateBezierTrajectory(from, to, numPoints)
	
	// 更新最后鼠标位置
	h.lastMousePos = to
	
	return trajectory
}

// generateBezierTrajectory 生成贝塞尔曲线轨迹
func (h *Humanizer) generateBezierTrajectory(from, to Point, numPoints int) []Point {
	// 生成控制点
	controlPoint1 := h.generateControlPoint(from, to, 0.3)
	controlPoint2 := h.generateControlPoint(from, to, 0.7)
	
	trajectory := make([]Point, numPoints)
	
	for i := 0; i < numPoints; i++ {
		t := float64(i) / float64(numPoints-1)
		point := h.calculateBezierPoint(from, controlPoint1, controlPoint2, to, t)
		
		// 添加微小随机偏移
		point.X += (h.rng.Float64() - 0.5) * h.noiseLevel * 3
		point.Y += (h.rng.Float64() - 0.5) * h.noiseLevel * 3
		
		trajectory[i] = point
	}
	
	return trajectory
}

// generateControlPoint 生成贝塞尔曲线控制点
func (h *Humanizer) generateControlPoint(from, to Point, ratio float64) Point {
	// 基础控制点
	baseX := from.X + (to.X-from.X)*ratio
	baseY := from.Y + (to.Y-from.Y)*ratio
	
	// 添加随机偏移，模拟人类不完美的鼠标移动
	distance := h.calculateDistance(from, to)
	maxOffset := distance * 0.2 * h.noiseLevel // 最大偏移为距离的20%
	
	offsetX := (h.rng.Float64() - 0.5) * maxOffset
	offsetY := (h.rng.Float64() - 0.5) * maxOffset
	
	return Point{
		X: baseX + offsetX,
		Y: baseY + offsetY,
	}
}

// calculateBezierPoint 计算贝塞尔曲线上的点
func (h *Humanizer) calculateBezierPoint(p0, p1, p2, p3 Point, t float64) Point {
	// 三次贝塞尔曲线公式
	u := 1 - t
	tt := t * t
	uu := u * u
	uuu := uu * u
	ttt := tt * t
	
	x := uuu*p0.X + 3*uu*t*p1.X + 3*u*tt*p2.X + ttt*p3.X
	y := uuu*p0.Y + 3*uu*t*p1.Y + 3*u*tt*p2.Y + ttt*p3.Y
	
	return Point{X: x, Y: y}
}

// calculateDistance 计算两点间距离
func (h *Humanizer) calculateDistance(from, to Point) float64 {
	dx := to.X - from.X
	dy := to.Y - from.Y
	return math.Sqrt(dx*dx + dy*dy)
}

// SimulateTypingPattern 模拟打字模式
func (h *Humanizer) SimulateTypingPattern(text string) []models.Event {
	if h.noiseLevel == 0 {
		// 无噪声模式，直接返回输入事件
		return []models.Event{
			{
				Type: "input",
				Data: map[string]interface{}{
					"value": text,
				},
			},
		}
	}

	events := make([]models.Event, 0)
	
	// 模拟逐字符输入
	for i, char := range text {
		// 计算打字延迟
		delay := h.calculateTypingDelay(char, i)
		
		// 创建按键事件
		keyEvent := models.Event{
			Type:  "keypress",
			Delay: delay,
			Data: map[string]interface{}{
				"key":  string(char),
				"code": h.getKeyCode(char),
			},
		}
		
		events = append(events, keyEvent)
		
		// 偶尔添加停顿（模拟思考）
		if h.shouldAddPause(i, len(text)) {
			pauseDelay := h.generateThinkingPause()
			if len(events) > 0 {
				events[len(events)-1].Delay += pauseDelay
			}
		}
	}
	
	return events
}

// calculateTypingDelay 计算打字延迟
func (h *Humanizer) calculateTypingDelay(char rune, position int) int {
	baseDelay := 100 // 基础延迟100ms
	
	// 根据字符类型调整延迟
	switch {
	case char >= 'A' && char <= 'Z':
		baseDelay += 50 // 大写字母需要按Shift，延迟更长
	case char >= '0' && char <= '9':
		baseDelay += 20 // 数字稍慢
	case char == ' ':
		baseDelay += 30 // 空格稍慢
	case char == '.' || char == ',' || char == '!' || char == '?':
		baseDelay += 100 // 标点符号后通常有停顿
	}
	
	// 添加随机变化
	variance := int(float64(baseDelay) * h.noiseLevel * 0.5)
	if variance > 0 {
		baseDelay += h.rng.Intn(variance*2) - variance
	}
	
	// 确保延迟不为负数
	if baseDelay < 10 {
		baseDelay = 10
	}
	
	return baseDelay
}

// shouldAddPause 判断是否应该添加停顿
func (h *Humanizer) shouldAddPause(position, totalLength int) bool {
	// 在句子中间、逗号后、或者较长文本中随机添加停顿
	pauseProbability := h.noiseLevel * 0.1 // 10%的概率
	
	// 在特定位置增加停顿概率
	if position > 0 && position < totalLength-1 {
		if position%10 == 0 { // 每10个字符
			pauseProbability += 0.3
		}
	}
	
	return h.rng.Float64() < pauseProbability
}

// generateThinkingPause 生成思考停顿时间
func (h *Humanizer) generateThinkingPause() int {
	// 思考停顿：200ms-1500ms
	baseDelay := 200 + h.rng.Intn(1300)
	return int(float64(baseDelay) * h.noiseLevel)
}

// getKeyCode 获取按键代码
func (h *Humanizer) getKeyCode(char rune) string {
	switch {
	case char >= 'a' && char <= 'z':
		return "Key" + string(char-'a'+'A')
	case char >= 'A' && char <= 'Z':
		return "Key" + string(char)
	case char >= '0' && char <= '9':
		return "Digit" + string(char)
	case char == ' ':
		return "Space"
	case char == '.':
		return "Period"
	case char == ',':
		return "Comma"
	default:
		return "Unknown"
	}
}

// SimulateScrollBehavior 模拟滚动行为
func (h *Humanizer) SimulateScrollBehavior(targetY float64) []models.Event {
	if h.noiseLevel == 0 {
		return []models.Event{
			{
				Type: "scroll",
				Data: map[string]interface{}{
					"deltaY": targetY,
				},
			},
		}
	}

	events := make([]models.Event, 0)
	
	// 将大的滚动分解为多个小滚动
	remainingY := targetY
	
	for math.Abs(remainingY) > 10 {
		// 计算本次滚动距离
		scrollStep := h.calculateScrollStep(remainingY)
		
		// 创建滚动事件
		scrollEvent := models.Event{
			Type:  "scroll",
			Delay: h.generateScrollDelay(),
			Data: map[string]interface{}{
				"deltaY": scrollStep,
			},
		}
		
		events = append(events, scrollEvent)
		remainingY -= scrollStep
	}
	
	// 添加最后的小滚动
	if math.Abs(remainingY) > 0 {
		scrollEvent := models.Event{
			Type:  "scroll",
			Delay: h.generateScrollDelay(),
			Data: map[string]interface{}{
				"deltaY": remainingY,
			},
		}
		events = append(events, scrollEvent)
	}
	
	return events
}

// calculateScrollStep 计算滚动步长
func (h *Humanizer) calculateScrollStep(remaining float64) float64 {
	// 基础步长
	baseStep := 120.0
	
	// 根据剩余距离调整步长
	if math.Abs(remaining) < baseStep {
		return remaining
	}
	
	// 添加随机变化
	variance := baseStep * h.noiseLevel * 0.3
	step := baseStep + (h.rng.Float64()-0.5)*variance
	
	// 保持方向一致
	if remaining < 0 {
		step = -step
	}
	
	return step
}

// generateScrollDelay 生成滚动延迟
func (h *Humanizer) generateScrollDelay() int {
	baseDelay := 50 + h.rng.Intn(100) // 50-150ms
	return int(float64(baseDelay) * (1 + h.noiseLevel))
}
