package connection

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"web3-control/internal/models"
)

// AuthManager 认证管理器
type AuthManager struct {
	clientID  string
	authToken string
	name      string
	group     string
	isMaster  bool
}

// NewAuthManager 创建认证管理器
func NewAuthManager(clientID, authToken, name, group string, isMaster bool) *AuthManager {
	// 如果没有提供客户端ID，自动生成一个
	if clientID == "" {
		clientID = generateClientID()
	}

	return &AuthManager{
		clientID:  clientID,
		authToken: authToken,
		name:      name,
		group:     group,
		isMaster:  isMaster,
	}
}

// GetClientID 获取客户端ID
func (a *AuthManager) GetClientID() string {
	return a.clientID
}

// GetAuthToken 获取认证令牌
func (a *AuthManager) GetAuthToken() string {
	return a.authToken
}

// GetName 获取客户端名称
func (a *AuthManager) GetName() string {
	return a.name
}

// GetGroup 获取分组
func (a *AuthManager) GetGroup() string {
	return a.group
}

// IsMaster 是否主控客户端
func (a *AuthManager) IsMaster() bool {
	return a.isMaster
}

// CreateRegisterRequest 创建注册请求
func (a *AuthManager) CreateRegisterRequest() *models.ClientRegisterRequest {
	return &models.ClientRegisterRequest{
		ClientID:  a.clientID,
		AuthToken: a.authToken,
		Name:      a.name,
		Group:     a.group,
		IsMaster:  a.isMaster,
	}
}

// CreateAuthMessage 创建认证消息
func (a *AuthManager) CreateAuthMessage() *models.WebSocketMessage {
	return &models.WebSocketMessage{
		Type:      "auth",
		Timestamp: time.Now().Unix(),
		Data:      a.CreateRegisterRequest(),
	}
}

// ValidateAuthResponse 验证认证响应
func (a *AuthManager) ValidateAuthResponse(response *models.ClientRegisterResponse) error {
	if response.Code != 0 {
		return fmt.Errorf("认证失败: %s", response.Message)
	}

	if response.Data.ClientID != a.clientID {
		return fmt.Errorf("客户端ID不匹配: 期望 %s, 实际 %s", a.clientID, response.Data.ClientID)
	}

	return nil
}

// generateClientID 生成客户端ID
// 格式：16位大写字母、数字、连字符的组合
func generateClientID() string {
	// 生成8字节随机数据
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳作为后备方案
		timestamp := time.Now().UnixNano()
		return fmt.Sprintf("CLIENT-%X", timestamp)
	}

	// 转换为十六进制字符串
	hex := strings.ToUpper(hex.EncodeToString(bytes))
	
	// 格式化为 XXXX-XXXX-XXXX-XXXX 格式
	if len(hex) >= 16 {
		return fmt.Sprintf("%s-%s-%s-%s", 
			hex[0:4], hex[4:8], hex[8:12], hex[12:16])
	}
	
	// 如果长度不够，补充随机字符
	for len(hex) < 16 {
		hex += "0"
	}
	
	return fmt.Sprintf("%s-%s-%s-%s", 
		hex[0:4], hex[4:8], hex[8:12], hex[12:16])
}

// IsValidClientID 验证客户端ID格式
func IsValidClientID(clientID string) bool {
	if len(clientID) == 0 {
		return false
	}
	
	// 检查是否包含非法字符
	for _, char := range clientID {
		if !((char >= 'A' && char <= 'Z') || 
			 (char >= '0' && char <= '9') || 
			 char == '-') {
			return false
		}
	}
	
	return true
}

// SetClientID 设置客户端ID
func (a *AuthManager) SetClientID(clientID string) error {
	if !IsValidClientID(clientID) {
		return fmt.Errorf("无效的客户端ID格式: %s", clientID)
	}
	a.clientID = clientID
	return nil
}

// SetAuthToken 设置认证令牌
func (a *AuthManager) SetAuthToken(authToken string) {
	a.authToken = authToken
}

// SetName 设置客户端名称
func (a *AuthManager) SetName(name string) {
	a.name = name
}

// SetGroup 设置分组
func (a *AuthManager) SetGroup(group string) {
	a.group = group
}

// SetIsMaster 设置是否主控
func (a *AuthManager) SetIsMaster(isMaster bool) {
	a.isMaster = isMaster
}

// UpdateFromConfig 从配置更新认证信息
func (a *AuthManager) UpdateFromConfig(clientID, authToken, name, group string, isMaster bool) {
	if clientID != "" {
		a.clientID = clientID
	}
	if authToken != "" {
		a.authToken = authToken
	}
	if name != "" {
		a.name = name
	}
	if group != "" {
		a.group = group
	}
	a.isMaster = isMaster
}

// String 返回认证信息的字符串表示
func (a *AuthManager) String() string {
	masterStr := "从控"
	if a.isMaster {
		masterStr = "主控"
	}
	return fmt.Sprintf("客户端[ID:%s, 名称:%s, 分组:%s, 类型:%s]", 
		a.clientID, a.name, a.group, masterStr)
}
