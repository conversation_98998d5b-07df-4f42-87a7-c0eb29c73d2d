package connection

import (
	"context"
	"fmt"
	"log"
	"net/url"
	"sync"
	"time"

	"web3-control/internal/models"

	"github.com/gorilla/websocket"
)

// MessageHandler 消息处理器函数类型
type MessageHandler func(*models.WebSocketMessage) error

// ConnectionManager WebSocket连接管理器
type ConnectionManager struct {
	serverURL       string
	conn            *websocket.Conn
	authManager     *AuthManager
	messageHandlers map[string]MessageHandler
	isConnected     bool
	isReconnecting  bool
	ctx             context.Context
	cancel          context.CancelFunc
	mutex           sync.RWMutex

	// 重连配置
	maxRetries int
	retryInterval time.Duration

	// 心跳配置
	heartbeatInterval time.Duration
	lastPong          time.Time

	// 消息队列
	sendQueue chan *models.WebSocketMessage
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager(serverURL string, authManager *AuthManager) *ConnectionManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &ConnectionManager{
		serverURL:         serverURL,
		authManager:       authManager,
		messageHandlers:   make(map[string]MessageHandler),
		ctx:               ctx,
		cancel:            cancel,
		maxRetries:        10,
		retryInterval:     5 * time.Second,
		heartbeatInterval: 30 * time.Second,
		sendQueue:         make(chan *models.WebSocketMessage, 100),
	}
}

// SetReconnectConfig 设置重连配置
func (cm *ConnectionManager) SetReconnectConfig(maxRetries int, interval time.Duration) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.maxRetries = maxRetries
	cm.retryInterval = interval
}

// RegisterMessageHandler 注册消息处理器
func (cm *ConnectionManager) RegisterMessageHandler(msgType string, handler MessageHandler) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.messageHandlers[msgType] = handler
}

// Connect 连接到服务器
func (cm *ConnectionManager) Connect() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if cm.isConnected {
		return fmt.Errorf("已经连接到服务器")
	}

	// 解析服务器URL
	u, err := url.Parse(cm.serverURL)
	if err != nil {
		return fmt.Errorf("无效的服务器URL: %v", err)
	}

	log.Printf("正在连接到服务器: %s", cm.serverURL)

	// 建立WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		return fmt.Errorf("连接服务器失败: %v", err)
	}

	cm.conn = conn
	cm.isConnected = true
	cm.lastPong = time.Now()

	log.Printf("WebSocket连接建立成功")

	// 发送认证消息
	if err := cm.authenticate(); err != nil {
		cm.conn.Close()
		cm.isConnected = false
		return fmt.Errorf("认证失败: %v", err)
	}

	// 启动消息处理协程
	go cm.messageLoop()
	go cm.heartbeatLoop()
	go cm.sendLoop()

	log.Printf("客户端连接成功: %s", cm.authManager.String())

	return nil
}

// Disconnect 断开连接
func (cm *ConnectionManager) Disconnect() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if !cm.isConnected {
		return nil
	}

	log.Printf("正在断开连接...")

	// 取消上下文
	cm.cancel()

	// 关闭连接
	if cm.conn != nil {
		cm.conn.Close()
	}

	cm.isConnected = false

	log.Printf("连接已断开")

	return nil
}

// SendMessage 发送消息
func (cm *ConnectionManager) SendMessage(msg *models.WebSocketMessage) error {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if !cm.isConnected {
		return fmt.Errorf("未连接到服务器")
	}

	// 将消息放入发送队列
	select {
	case cm.sendQueue <- msg:
		return nil
	default:
		return fmt.Errorf("发送队列已满")
	}
}

// IsConnected 检查是否已连接
func (cm *ConnectionManager) IsConnected() bool {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	return cm.isConnected
}

// authenticate 执行认证
func (cm *ConnectionManager) authenticate() error {
	authMsg := cm.authManager.CreateAuthMessage()

	if err := cm.conn.WriteJSON(authMsg); err != nil {
		return fmt.Errorf("发送认证消息失败: %v", err)
	}

	// 等待认证响应
	cm.conn.SetReadDeadline(time.Now().Add(10 * time.Second))

	var response models.ClientRegisterResponse
	if err := cm.conn.ReadJSON(&response); err != nil {
		return fmt.Errorf("读取认证响应失败: %v", err)
	}

	// 验证认证响应
	if err := cm.authManager.ValidateAuthResponse(&response); err != nil {
		return err
	}

	// 清除读取超时
	cm.conn.SetReadDeadline(time.Time{})

	log.Printf("客户端认证成功")

	return nil
}

// messageLoop 消息处理循环
func (cm *ConnectionManager) messageLoop() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("消息处理循环异常: %v", r)
		}
	}()

	for {
		select {
		case <-cm.ctx.Done():
			return
		default:
			// 读取消息
			var msg models.WebSocketMessage
			if err := cm.conn.ReadJSON(&msg); err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Printf("WebSocket连接异常关闭: %v", err)
				}

				// 连接断开，尝试重连
				cm.handleDisconnection()
				return
			}

			// 处理消息
			cm.handleMessage(&msg)
		}
	}
}

// handleMessage 处理接收到的消息
func (cm *ConnectionManager) handleMessage(msg *models.WebSocketMessage) {
	cm.mutex.RLock()
	handler, exists := cm.messageHandlers[msg.Type]
	cm.mutex.RUnlock()

	if exists {
		if err := handler(msg); err != nil {
			log.Printf("处理消息失败 [类型:%s]: %v", msg.Type, err)
		}
	} else {
		log.Printf("未知消息类型: %s", msg.Type)
	}
}

// handleDisconnection 处理连接断开
func (cm *ConnectionManager) handleDisconnection() {
	cm.mutex.Lock()
	cm.isConnected = false
	cm.mutex.Unlock()

	if cm.isReconnecting {
		return
	}

	cm.isReconnecting = true
	defer func() { cm.isReconnecting = false }()

	log.Printf("连接断开，开始重连...")

	for i := 0; i < cm.maxRetries; i++ {
		select {
		case <-cm.ctx.Done():
			return
		case <-time.After(cm.retryInterval):
			if err := cm.Connect(); err != nil {
				log.Printf("重连失败 (%d/%d): %v", i+1, cm.maxRetries, err)
			} else {
				log.Printf("重连成功")
				return
			}
		}
	}

	log.Printf("重连失败，已达到最大重试次数")
}

// heartbeatLoop 心跳循环
func (cm *ConnectionManager) heartbeatLoop() {
	ticker := time.NewTicker(cm.heartbeatInterval)
	defer ticker.Stop()

	for {
		select {
		case <-cm.ctx.Done():
			return
		case <-ticker.C:
			if !cm.IsConnected() {
				continue
			}

			// 发送心跳消息
			heartbeatMsg := &models.WebSocketMessage{
				Type:      "heartbeat",
				Timestamp: time.Now().Unix(),
				Data:      "ping",
			}

			if err := cm.SendMessage(heartbeatMsg); err != nil {
				log.Printf("发送心跳失败: %v", err)
			}

			// 检查是否超时
			if time.Since(cm.lastPong) > cm.heartbeatInterval*2 {
				log.Printf("心跳超时，连接可能已断开")
				cm.handleDisconnection()
				return
			}
		}
	}
}

// sendLoop 发送消息循环
func (cm *ConnectionManager) sendLoop() {
	for {
		select {
		case <-cm.ctx.Done():
			return
		case msg := <-cm.sendQueue:
			if !cm.IsConnected() {
				continue
			}

			if err := cm.conn.WriteJSON(msg); err != nil {
				log.Printf("发送消息失败: %v", err)
				cm.handleDisconnection()
				return
			}
		}
	}
}

// UpdateLastPong 更新最后pong时间
func (cm *ConnectionManager) UpdateLastPong() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.lastPong = time.Now()
}

// GetAuthManager 获取认证管理器
func (cm *ConnectionManager) GetAuthManager() *AuthManager {
	return cm.authManager
}
