package browser

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"time"

	"web3-control/internal/models"
	"web3-control/module/client/noise"

	"github.com/chromedp/chromedp"
)

// BrowserLauncher 浏览器启动器
type BrowserLauncher struct {
	noiseGenerator *noise.NoiseGenerator
}

// NewBrowserLauncher 创建浏览器启动器
func NewBrowserLauncher(noiseLevel float64) *BrowserLauncher {
	return &BrowserLauncher{
		noiseGenerator: noise.NewNoiseGenerator(noiseLevel),
	}
}

// LaunchBrowser 启动浏览器
func (bl *BrowserLauncher) LaunchBrowser(config *models.BrowserConfig) (*BrowserInstance, error) {
	log.Printf("正在启动浏览器: %s", config.Name)

	// 查找Chrome可执行文件路径
	chromePath := ""
	if chromePath == "" {
		var err error
		chromePath, err = bl.findChromePath()
		if err != nil {
			return nil, fmt.Errorf("找不到Chrome可执行文件: %v", err)
		}
	}

	// 准备用户数据目录
	userDataDir := config.UserDir
	if userDataDir == "" {
		userDataDir = bl.createTempUserDataDir(config.BrowserID)
	}

	// 确保用户数据目录存在
	if err := os.MkdirAll(userDataDir, 0755); err != nil {
		return nil, fmt.Errorf("创建用户数据目录失败: %v", err)
	}

	// 解析调试端口
	debugPort, err := strconv.Atoi(config.Port)
	if err != nil || debugPort <= 0 {
		debugPort = 9222
	}

	// 构建启动参数
	args := bl.buildChromeArgs(config, userDataDir, debugPort)

	// 启动Chrome进程
	ctx, cancel := context.WithCancel(context.Background())
	cmd := exec.CommandContext(ctx, chromePath, args...)

	// 设置环境变量
	cmd.Env = os.Environ()

	if err := cmd.Start(); err != nil {
		cancel()
		return nil, fmt.Errorf("启动Chrome失败: %v", err)
	}

	// 等待Chrome启动
	time.Sleep(2 * time.Second)

	// 创建CDP连接
	cdpCtx, err := bl.createCDPContext(debugPort)
	if err != nil {
		cmd.Process.Kill()
		cancel()
		return nil, fmt.Errorf("创建CDP连接失败: %v", err)
	}

	// 创建浏览器实例
	instance := &BrowserInstance{
		ID:          config.BrowserID,
		Name:        config.Name,
		Config:      config,
		Process:     cmd,
		Context:     cdpCtx,
		Cancel:      cancel,
		Status:      "running",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 应用浏览器配置
	if err := bl.applyBrowserConfig(instance); err != nil {
		log.Printf("应用浏览器配置失败: %v", err)
	}

	log.Printf("浏览器启动成功: %s (PID: %d)", config.Name, cmd.Process.Pid)

	return instance, nil
}

// findChromePath 查找Chrome可执行文件路径
func (bl *BrowserLauncher) findChromePath() (string, error) {
	var possiblePaths []string

	switch runtime.GOOS {
	case "windows":
		possiblePaths = []string{
			`C:\Program Files\Google\Chrome\Application\chrome.exe`,
			`C:\Program Files (x86)\Google\Chrome\Application\chrome.exe`,
			`C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe`,
		}
	case "darwin":
		possiblePaths = []string{
			"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
			"/Applications/Chromium.app/Contents/MacOS/Chromium",
		}
	case "linux":
		possiblePaths = []string{
			"/usr/bin/google-chrome",
			"/usr/bin/google-chrome-stable",
			"/usr/bin/chromium",
			"/usr/bin/chromium-browser",
			"/snap/bin/chromium",
		}
	}

	// 检查可能的路径
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			return path, nil
		}
	}

	// 尝试在PATH中查找
	if path, err := exec.LookPath("chrome"); err == nil {
		return path, nil
	}
	if path, err := exec.LookPath("google-chrome"); err == nil {
		return path, nil
	}
	if path, err := exec.LookPath("chromium"); err == nil {
		return path, nil
	}

	return "", fmt.Errorf("未找到Chrome可执行文件")
}

// createTempUserDataDir 创建临时用户数据目录
func (bl *BrowserLauncher) createTempUserDataDir(browserID string) string {
	tempDir := os.TempDir()
	userDataDir := filepath.Join(tempDir, "chrome-"+browserID)
	return userDataDir
}

// buildChromeArgs 构建Chrome启动参数
func (bl *BrowserLauncher) buildChromeArgs(config *models.BrowserConfig, userDataDir string, debugPort int) []string {
	args := []string{
		"--remote-debugging-port=" + strconv.Itoa(debugPort),
		"--user-data-dir=" + userDataDir,
		"--no-first-run",
		"--no-default-browser-check",
		"--disable-background-timer-throttling",
		"--disable-backgrounding-occluded-windows",
		"--disable-renderer-backgrounding",
		"--disable-features=TranslateUI",
		"--disable-ipc-flooding-protection",
	}

	// 添加代理设置
	if config.Proxy != "" {
		args = append(args, "--proxy-server="+config.Proxy)
	}

	// 使用随机User-Agent
	randomUA := bl.noiseGenerator.GenerateRandomUserAgent()
	args = append(args, "--user-agent="+randomUA)

	// 使用随机窗口大小
	width, height := bl.noiseGenerator.GenerateRandomWindowSize()
	args = append(args, fmt.Sprintf("--window-size=%d,%d", width, height))

	// 添加额外参数
	if len(config.AdditionalArgs) > 0 {
		args = append(args, config.AdditionalArgs...)
	}

	return args
}

// createCDPContext 创建CDP上下文
func (bl *BrowserLauncher) createCDPContext(debugPort int) (context.Context, error) {
	// 创建远程分配器连接到已启动的Chrome实例
	allocCtx, _ := chromedp.NewRemoteAllocator(context.Background(),
		fmt.Sprintf("ws://localhost:%d", debugPort))

	// 创建CDP上下文
	ctx, _ := chromedp.NewContext(allocCtx)

	// 测试连接
	err := chromedp.Run(ctx, chromedp.Navigate("about:blank"))
	if err != nil {
		return nil, fmt.Errorf("CDP连接测试失败: %v", err)
	}

	return ctx, nil
}

// applyBrowserConfig 应用浏览器配置
func (bl *BrowserLauncher) applyBrowserConfig(instance *BrowserInstance) error {
	// 设置视口大小
	width, height := bl.noiseGenerator.GenerateRandomWindowSize()
	err := chromedp.Run(instance.Context,
		chromedp.EmulateViewport(int64(width), int64(height)),
	)
	if err != nil {
		return fmt.Errorf("设置视口大小失败: %v", err)
	}

	return nil
}

// CloseBrowser 关闭浏览器
func (bl *BrowserLauncher) CloseBrowser(instance *BrowserInstance) error {
	log.Printf("正在关闭浏览器: %s", instance.Name)

	// 取消上下文
	if instance.Cancel != nil {
		instance.Cancel()
	}

	// 终止进程
	if instance.Process != nil && instance.Process.Process != nil {
		if err := instance.Process.Process.Kill(); err != nil {
			log.Printf("终止浏览器进程失败: %v", err)
		}
	}

	// 更新状态
	instance.Status = "closed"
	instance.UpdatedAt = time.Now()

	log.Printf("浏览器已关闭: %s", instance.Name)

	return nil
}
