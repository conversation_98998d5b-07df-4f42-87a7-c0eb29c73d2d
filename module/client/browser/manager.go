package browser

import (
	"context"
	"fmt"
	"log"
	"os/exec"
	"sync"
	"time"

	"web3-control/internal/models"
)

// BrowserInstance 浏览器实例
type BrowserInstance struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Config      *models.BrowserConfig  `json:"config"`
	Process     *exec.Cmd              `json:"-"`
	Context     context.Context        `json:"-"`
	Cancel      context.CancelFunc     `json:"-"`
	Status      string                 `json:"status"`
	URL         string                 `json:"url"`
	MemoryUsage float64               `json:"memory_usage"`
	CPUUsage    float64               `json:"cpu_usage"`
	CreatedAt   time.Time             `json:"created_at"`
	UpdatedAt   time.Time             `json:"updated_at"`
	mutex       sync.RWMutex          `json:"-"`
}

// BrowserManager 浏览器管理器接口
type BrowserManager interface {
	LaunchBrowser(config *models.BrowserConfig) (*BrowserInstance, error)
	CloseBrowser(browserID string) error
	GetBrowser(browserID string) (*BrowserInstance, error)
	ListBrowsers() []*BrowserInstance
	UpdateBrowserStatus(browserID string, status *models.BrowserStatus) error
	GetBrowserStatus(browserID string) (*models.BrowserStatus, error)
	GetRunningBrowsers() []*BrowserInstance
}

// DefaultBrowserManager 默认浏览器管理器实现
type DefaultBrowserManager struct {
	browsers map[string]*BrowserInstance
	launcher *BrowserLauncher
	mutex    sync.RWMutex
}

// NewBrowserManager 创建浏览器管理器
func NewBrowserManager(noiseLevel float64) BrowserManager {
	return &DefaultBrowserManager{
		browsers: make(map[string]*BrowserInstance),
		launcher: NewBrowserLauncher(noiseLevel),
	}
}

// LaunchBrowser 启动浏览器
func (bm *DefaultBrowserManager) LaunchBrowser(config *models.BrowserConfig) (*BrowserInstance, error) {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	// 检查是否已存在
	if _, exists := bm.browsers[config.BrowserID]; exists {
		return nil, fmt.Errorf("浏览器实例已存在: %s", config.BrowserID)
	}

	// 启动浏览器
	instance, err := bm.launcher.LaunchBrowser(config)
	if err != nil {
		return nil, err
	}

	// 添加到管理器
	bm.browsers[config.BrowserID] = instance

	// 启动状态监控
	go bm.monitorBrowser(instance)

	log.Printf("浏览器管理器: 已添加浏览器实例 %s", config.BrowserID)

	return instance, nil
}

// CloseBrowser 关闭浏览器
func (bm *DefaultBrowserManager) CloseBrowser(browserID string) error {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	instance, exists := bm.browsers[browserID]
	if !exists {
		return fmt.Errorf("浏览器实例不存在: %s", browserID)
	}

	// 关闭浏览器
	if err := bm.launcher.CloseBrowser(instance); err != nil {
		return err
	}

	// 从管理器中移除
	delete(bm.browsers, browserID)

	log.Printf("浏览器管理器: 已移除浏览器实例 %s", browserID)

	return nil
}

// GetBrowser 获取浏览器实例
func (bm *DefaultBrowserManager) GetBrowser(browserID string) (*BrowserInstance, error) {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	instance, exists := bm.browsers[browserID]
	if !exists {
		return nil, fmt.Errorf("浏览器实例不存在: %s", browserID)
	}

	return instance, nil
}

// ListBrowsers 列出所有浏览器实例
func (bm *DefaultBrowserManager) ListBrowsers() []*BrowserInstance {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	instances := make([]*BrowserInstance, 0, len(bm.browsers))
	for _, instance := range bm.browsers {
		instances = append(instances, instance)
	}

	return instances
}

// UpdateBrowserStatus 更新浏览器状态
func (bm *DefaultBrowserManager) UpdateBrowserStatus(browserID string, status *models.BrowserStatus) error {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	instance, exists := bm.browsers[browserID]
	if !exists {
		return fmt.Errorf("浏览器实例不存在: %s", browserID)
	}

	instance.mutex.Lock()
	defer instance.mutex.Unlock()

	// 更新状态
	instance.Status = status.Status
	instance.URL = status.URL
	instance.MemoryUsage = status.MemoryUsage
	instance.CPUUsage = status.CPUUsage
	instance.UpdatedAt = time.Now()

	return nil
}

// GetBrowserStatus 获取浏览器状态
func (bm *DefaultBrowserManager) GetBrowserStatus(browserID string) (*models.BrowserStatus, error) {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	instance, exists := bm.browsers[browserID]
	if !exists {
		return nil, fmt.Errorf("浏览器实例不存在: %s", browserID)
	}

	// 获取实时状态
	status := &models.BrowserStatus{
		BrowserID:   instance.ID,
		Status:      instance.Status,
		URL:         instance.URL,
		MemoryUsage: instance.MemoryUsage,
		CPUUsage:    instance.CPUUsage,
		UpdatedAt:   time.Now(),
	}

	// 检查进程状态
	if instance.Process != nil && instance.Process.Process != nil {
		if instance.Process.ProcessState == nil {
			status.Status = "running"
		} else {
			status.Status = "closed"
		}
	}

	return status, nil
}

// monitorBrowser 监控浏览器状态
func (bm *DefaultBrowserManager) monitorBrowser(instance *BrowserInstance) {
	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 检查浏览器是否还在运行
			if !bm.isBrowserRunning(instance) {
				log.Printf("检测到浏览器 %s 已停止运行", instance.ID)

				// 更新状态
				instance.mutex.Lock()
				instance.Status = "closed"
				instance.UpdatedAt = time.Now()
				instance.mutex.Unlock()

				// 从管理器中移除
				bm.mutex.Lock()
				delete(bm.browsers, instance.ID)
				bm.mutex.Unlock()

				return
			}

			// 更新状态信息
			bm.updateBrowserInfo(instance)

		case <-instance.Context.Done():
			// 上下文被取消，停止监控
			return
		}
	}
}

// isBrowserRunning 检查浏览器是否在运行
func (bm *DefaultBrowserManager) isBrowserRunning(instance *BrowserInstance) bool {
	if instance.Process == nil || instance.Process.Process == nil {
		return false
	}

	// 检查进程状态
	if instance.Process.ProcessState != nil {
		return false
	}

	// 尝试发送信号0来检查进程是否存在
	return instance.Process.Process.Signal(nil) == nil
}

// updateBrowserInfo 更新浏览器信息
func (bm *DefaultBrowserManager) updateBrowserInfo(instance *BrowserInstance) {
	instance.mutex.Lock()
	defer instance.mutex.Unlock()

	// 更新时间戳
	instance.UpdatedAt = time.Now()

	// TODO: 实现内存和CPU使用率的获取
	// 这里可以通过系统调用或第三方库来获取进程的资源使用情况
}

// Shutdown 关闭所有浏览器
func (bm *DefaultBrowserManager) Shutdown() error {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	log.Printf("正在关闭所有浏览器实例...")

	var errors []error
	for browserID := range bm.browsers {
		if err := bm.CloseBrowser(browserID); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("关闭部分浏览器失败: %v", errors)
	}

	log.Printf("所有浏览器实例已关闭")
	return nil
}

// GetBrowserCount 获取浏览器数量
func (bm *DefaultBrowserManager) GetBrowserCount() int {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	return len(bm.browsers)
}

// GetRunningBrowsers 获取运行中的浏览器
func (bm *DefaultBrowserManager) GetRunningBrowsers() []*BrowserInstance {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	var running []*BrowserInstance
	for _, instance := range bm.browsers {
		if instance.Status == "running" {
			running = append(running, instance)
		}
	}

	return running
}
