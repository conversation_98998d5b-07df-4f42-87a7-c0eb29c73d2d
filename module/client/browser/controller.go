package browser

import (
	"fmt"
	"log"
	"time"

	"web3-control/internal/models"

	"github.com/chromedp/chromedp"
)

// BrowserController 浏览器控制器
type BrowserController struct {
	manager BrowserManager
}

// NewBrowserController 创建浏览器控制器
func NewBrowserController(manager BrowserManager) *BrowserController {
	return &BrowserController{
		manager: manager,
	}
}

// ExecuteEvent 执行事件
func (bc *BrowserController) ExecuteEvent(browserID string, event *models.Event) error {
	// 获取浏览器实例
	instance, err := bc.manager.GetBrowser(browserID)
	if err != nil {
		return fmt.Errorf("获取浏览器实例失败: %v", err)
	}

	// 检查浏览器状态
	if instance.Status != "running" {
		return fmt.Errorf("浏览器未运行: %s", browserID)
	}

	// 添加延迟
	if event.Delay > 0 {
		time.Sleep(time.Duration(event.Delay) * time.Millisecond)
	}

	// 根据事件类型执行相应操作
	switch event.Type {
	case "click":
		return bc.executeClick(instance, event)
	case "dblclick":
		return bc.executeDoubleClick(instance, event)
	case "input":
		return bc.executeInput(instance, event)
	case "keypress", "keydown", "keyup":
		return bc.executeKey(instance, event)
	case "scroll":
		return bc.executeScroll(instance, event)
	case "navigate":
		return bc.executeNavigate(instance, event)
	case "focus":
		return bc.executeFocus(instance, event)
	case "blur":
		return bc.executeBlur(instance, event)
	default:
		return fmt.Errorf("不支持的事件类型: %s", event.Type)
	}
}

// executeClick 执行点击事件
func (bc *BrowserController) executeClick(instance *BrowserInstance, event *models.Event) error {
	selector, ok := event.Data["selector"].(string)
	if !ok {
		// 如果没有选择器，使用坐标点击
		return bc.executeClickByCoordinates(instance, event)
	}

	// 使用选择器点击
	err := chromedp.Run(instance.Context,
		chromedp.WaitVisible(selector, chromedp.ByQuery),
		chromedp.Click(selector, chromedp.ByQuery),
	)

	if err != nil {
		return fmt.Errorf("点击元素失败 [%s]: %v", selector, err)
	}

	log.Printf("执行点击: %s", selector)
	return nil
}

// executeClickByCoordinates 通过坐标执行点击
func (bc *BrowserController) executeClickByCoordinates(instance *BrowserInstance, event *models.Event) error {
	x, xOk := event.Data["x"].(float64)
	y, yOk := event.Data["y"].(float64)

	if !xOk || !yOk {
		return fmt.Errorf("缺少点击坐标")
	}

	// 使用JavaScript执行坐标点击
	script := fmt.Sprintf(`
		(function() {
			var event = new MouseEvent('click', {
				view: window,
				bubbles: true,
				cancelable: true,
				clientX: %f,
				clientY: %f
			});
			var element = document.elementFromPoint(%f, %f);
			if (element) {
				element.dispatchEvent(event);
				return true;
			}
			return false;
		})();
	`, x, y, x, y)

	var result bool
	err := chromedp.Run(instance.Context,
		chromedp.Evaluate(script, &result),
	)

	if err != nil {
		return fmt.Errorf("坐标点击失败 [%.0f,%.0f]: %v", x, y, err)
	}

	log.Printf("执行坐标点击: (%.0f, %.0f)", x, y)
	return nil
}

// executeDoubleClick 执行双击事件
func (bc *BrowserController) executeDoubleClick(instance *BrowserInstance, event *models.Event) error {
	selector, ok := event.Data["selector"].(string)
	if !ok {
		return fmt.Errorf("缺少选择器")
	}

	err := chromedp.Run(instance.Context,
		chromedp.WaitVisible(selector, chromedp.ByQuery),
		chromedp.DoubleClick(selector, chromedp.ByQuery),
	)

	if err != nil {
		return fmt.Errorf("双击元素失败 [%s]: %v", selector, err)
	}

	log.Printf("执行双击: %s", selector)
	return nil
}

// executeInput 执行输入事件
func (bc *BrowserController) executeInput(instance *BrowserInstance, event *models.Event) error {
	selector, selectorOk := event.Data["selector"].(string)
	value, valueOk := event.Data["value"].(string)

	if !selectorOk || !valueOk {
		return fmt.Errorf("缺少选择器或输入值")
	}

	// 检查是否需要清空输入框
	clear, _ := event.Data["clear"].(bool)

	var actions []chromedp.Action
	actions = append(actions, chromedp.WaitVisible(selector, chromedp.ByQuery))
	actions = append(actions, chromedp.Focus(selector, chromedp.ByQuery))

	if clear {
		actions = append(actions, chromedp.Clear(selector, chromedp.ByQuery))
	}

	actions = append(actions, chromedp.SendKeys(selector, value, chromedp.ByQuery))

	err := chromedp.Run(instance.Context, actions...)
	if err != nil {
		return fmt.Errorf("输入文本失败 [%s]: %v", selector, err)
	}

	log.Printf("执行输入: %s = %s", selector, value)
	return nil
}

// executeKey 执行按键事件
func (bc *BrowserController) executeKey(instance *BrowserInstance, event *models.Event) error {
	key, ok := event.Data["key"].(string)
	if !ok {
		return fmt.Errorf("缺少按键信息")
	}

	// 构建按键事件的JavaScript
	script := fmt.Sprintf(`
		(function() {
			var event = new KeyboardEvent('%s', {
				key: '%s',
				code: '%s',
				bubbles: true,
				cancelable: true
			});
			document.activeElement.dispatchEvent(event);
			return true;
		})();
	`, event.Type, key, event.Data["code"])

	var result bool
	err := chromedp.Run(instance.Context,
		chromedp.Evaluate(script, &result),
	)

	if err != nil {
		return fmt.Errorf("按键事件失败 [%s]: %v", key, err)
	}

	log.Printf("执行按键: %s", key)
	return nil
}

// executeScroll 执行滚动事件
func (bc *BrowserController) executeScroll(instance *BrowserInstance, event *models.Event) error {
	deltaY, deltaYOk := event.Data["deltaY"].(float64)
	deltaX, deltaXOk := event.Data["deltaX"].(float64)

	if !deltaYOk {
		deltaY = 0
	}
	if !deltaXOk {
		deltaX = 0
	}

	// 使用JavaScript执行滚动
	script := fmt.Sprintf(`
		window.scrollBy(%f, %f);
	`, deltaX, deltaY)

	err := chromedp.Run(instance.Context,
		chromedp.Evaluate(script, nil),
	)

	if err != nil {
		return fmt.Errorf("滚动失败: %v", err)
	}

	log.Printf("执行滚动: deltaX=%.0f, deltaY=%.0f", deltaX, deltaY)
	return nil
}

// executeNavigate 执行导航事件
func (bc *BrowserController) executeNavigate(instance *BrowserInstance, event *models.Event) error {
	url, ok := event.Data["url"].(string)
	if !ok {
		return fmt.Errorf("缺少导航URL")
	}

	err := chromedp.Run(instance.Context,
		chromedp.Navigate(url),
	)

	if err != nil {
		return fmt.Errorf("导航失败 [%s]: %v", url, err)
	}

	log.Printf("执行导航: %s", url)
	return nil
}

// executeFocus 执行焦点事件
func (bc *BrowserController) executeFocus(instance *BrowserInstance, event *models.Event) error {
	selector, ok := event.Data["selector"].(string)
	if !ok {
		return fmt.Errorf("缺少选择器")
	}

	err := chromedp.Run(instance.Context,
		chromedp.WaitVisible(selector, chromedp.ByQuery),
		chromedp.Focus(selector, chromedp.ByQuery),
	)

	if err != nil {
		return fmt.Errorf("设置焦点失败 [%s]: %v", selector, err)
	}

	log.Printf("执行焦点: %s", selector)
	return nil
}

// executeBlur 执行失焦事件
func (bc *BrowserController) executeBlur(instance *BrowserInstance, event *models.Event) error {
	selector, ok := event.Data["selector"].(string)
	if !ok {
		return fmt.Errorf("缺少选择器")
	}

	script := fmt.Sprintf(`
		(function() {
			var element = document.querySelector('%s');
			if (element) {
				element.blur();
				return true;
			}
			return false;
		})();
	`, selector)

	var result bool
	err := chromedp.Run(instance.Context,
		chromedp.Evaluate(script, &result),
	)

	if err != nil {
		return fmt.Errorf("失焦失败 [%s]: %v", selector, err)
	}

	log.Printf("执行失焦: %s", selector)
	return nil
}

// GetCurrentURL 获取当前页面URL
func (bc *BrowserController) GetCurrentURL(browserID string) (string, error) {
	instance, err := bc.manager.GetBrowser(browserID)
	if err != nil {
		return "", err
	}

	var url string
	err = chromedp.Run(instance.Context,
		chromedp.Location(&url),
	)

	if err != nil {
		return "", fmt.Errorf("获取URL失败: %v", err)
	}

	return url, nil
}

// GetPageTitle 获取页面标题
func (bc *BrowserController) GetPageTitle(browserID string) (string, error) {
	instance, err := bc.manager.GetBrowser(browserID)
	if err != nil {
		return "", err
	}

	var title string
	err = chromedp.Run(instance.Context,
		chromedp.Title(&title),
	)

	if err != nil {
		return "", fmt.Errorf("获取页面标题失败: %v", err)
	}

	return title, nil
}
