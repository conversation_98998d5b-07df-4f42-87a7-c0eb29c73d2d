package handler

import (
	"net/http"
	"strconv"

	"web3-control/module/server/service"

	"github.com/gin-gonic/gin"
)

// Handler HTTP处理器
type Handler struct {
	svc *service.Service
}

// NewHandler 创建新的处理器
func NewHandler(svc *service.Service) *Handler {
	return &Handler{svc: svc}
}

// RegisterRoutes 注册路由
func RegisterRoutes(r *gin.Engine, svc *service.Service) {
	h := NewHandler(svc)

	// 设置连接管理器
	svc.SetConnectionManager(GetConnectionManager())

	// WebSocket路由
	r.GET("/ws", h.HandleWebSocket)

	// API路由组
	api := r.Group("/api")
	{
		// 客户端管理
		api.GET("/clients", h.ListClients)
		api.POST("/clients", h.CreateClient)
		api.GET("/clients/online", h.GetOnlineClients)
		api.GET("/clients/:id", h.GetClient)
		api.PUT("/clients/:id", h.UpdateClient)
		api.DELETE("/clients/:id", h.RemoveClient)
		api.GET("/clients/:id/detail", h.GetClientDetail)
		api.POST("/clients/:id/toggle-status", h.ToggleClientStatus)
		api.POST("/clients/:id/send-command", h.SendCommandToClient)
		api.GET("/clients/group/:group", h.ListClientsByGroup)
		api.GET("/client-groups", h.GetClientGroups)

		// 浏览器管理
		api.GET("/browsers", h.ListBrowsers)
		api.POST("/browsers", h.CreateBrowser)
		api.GET("/browsers/:id", h.GetBrowser)
		api.PUT("/browsers/:id", h.UpdateBrowser)
		api.DELETE("/browsers/:id", h.DeleteBrowser)
		api.GET("/browsers/group/:group", h.ListBrowsersByGroup)
		api.GET("/browsers/client/:client_id", h.ListBrowsersByClientID)
		api.POST("/browsers/launch", h.LaunchBrowser)
		api.POST("/browsers/close", h.CloseBrowser)
		api.POST("/browsers/launch/group", h.LaunchBrowsersByGroup)
		api.POST("/browsers/close/group", h.CloseBrowsersByGroup)

		// 浏览器状态管理
		api.GET("/browser-status", h.ListBrowserStatuses)
		api.GET("/browser-status/:browser_id", h.GetBrowserStatus)

		// 代理管理
		api.GET("/proxies", h.ListProxies)
		api.POST("/proxies", h.CreateProxy)
		api.GET("/proxies/:id", h.GetProxy)
		api.PUT("/proxies/:id", h.UpdateProxy)
		api.DELETE("/proxies/:id", h.DeleteProxy)
		api.GET("/proxies/group/:group", h.ListProxiesByGroup)
		api.GET("/proxies/status/:status", h.ListProxiesByStatus)

		// 代理使用记录
		api.GET("/proxy-usage/proxy/:proxy_id", h.GetProxyUsageByProxy)
		api.GET("/proxy-usage/browser/:browser_id", h.GetProxyUsageByBrowser)

		// 录制管理
		api.GET("/recordings", h.ListRecordings)
		api.GET("/recordings/:id", h.GetRecording)
		api.POST("/recording/start", h.StartRecording)
		api.POST("/recording/stop", h.StopRecording)
		api.POST("/recording/play", h.PlayRecording)
		api.DELETE("/recordings/:id", h.DeleteRecording)

		// 监控管理
		api.GET("/monitor/system", h.GetSystemStats)
		api.GET("/monitor/performance", h.GetPerformanceStats)
		api.GET("/monitor/health", h.GetHealthStatus)
		api.GET("/monitor/dashboard", h.GetDashboardData)
	}

	// Web控制台
	r.Static("/static", "./web/static")
	r.LoadHTMLGlob("web/template/*")
	r.GET("/", h.ServeWebConsole)
}

// ListClients 获取所有客户端列表
func (h *Handler) ListClients(c *gin.Context) {
	clients, err := h.svc.ListClients()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    clients,
	})
}

// GetClient 获取指定客户端信息
func (h *Handler) GetClient(c *gin.Context) {
	id := c.Param("id")
	client, err := h.svc.GetClient(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "client not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    client,
	})
}

// ListClientsByGroup 获取指定分组的所有客户端
func (h *Handler) ListClientsByGroup(c *gin.Context) {
	group := c.Param("group")
	clients, err := h.svc.ListClientsByGroup(group)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    clients,
	})
}

// RemoveClient 移除客户端
func (h *Handler) RemoveClient(c *gin.Context) {
	id := c.Param("id")
	if err := h.svc.RemoveClient(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
	})
}

// ListRecordings 获取所有录制列表
func (h *Handler) ListRecordings(c *gin.Context) {
	recordings, err := h.svc.ListRecordings()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    recordings,
	})
}

// GetRecording 获取指定录制信息
func (h *Handler) GetRecording(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "invalid recording id",
		})
		return
	}

	recording, err := h.svc.GetRecording(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "recording not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    recording,
	})
}

// StartRecording 开始录制
func (h *Handler) StartRecording(c *gin.Context) {
	var req struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	if err := h.svc.StartRecording(req.Name, req.Description); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "录制已开始",
	})
}

// StopRecording 停止录制
func (h *Handler) StopRecording(c *gin.Context) {
	if err := h.svc.StopRecording(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "录制已停止",
	})
}

// PlayRecording 播放录制
func (h *Handler) PlayRecording(c *gin.Context) {
	var req struct {
		RecordingID uint     `json:"recording_id" binding:"required"`
		Group       string   `json:"group"`
		ClientIDs   []string `json:"client_ids"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	if err := h.svc.PlayRecording(req.RecordingID, req.Group, req.ClientIDs); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "录制播放已开始",
	})
}

// DeleteRecording 删除录制
func (h *Handler) DeleteRecording(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "invalid recording id",
		})
		return
	}

	if err := h.svc.DeleteRecording(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
	})
}

// ServeWebConsole 提供Web控制台页面
func (h *Handler) ServeWebConsole(c *gin.Context) {
	c.HTML(http.StatusOK, "index.html", gin.H{
		"title": "浏览器群控系统",
	})
}
