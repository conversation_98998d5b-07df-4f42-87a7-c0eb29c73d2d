package handler

import (
	"net/http"
	"strconv"

	"web3-control/internal/models/entity"

	"github.com/gin-gonic/gin"
)

// ===== 代理管理相关处理器 =====

// ListProxies 获取所有代理配置列表
func (h *Handler) ListProxies(c *gin.Context) {
	proxies, err := h.svc.ListProxies()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    proxies,
	})
}

// CreateProxy 创建代理配置
func (h *Handler) CreateProxy(c *gin.Context) {
	var req struct {
		Name     string `json:"name" binding:"required"`
		Hosts    string `json:"hosts" binding:"required"`
		Port     string `json:"port" binding:"required"`
		Username string `json:"username"`
		Password string `json:"password"`
		Group    string `json:"group"`
		Status   string `json:"status"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	// 设置默认状态
	if req.Status == "" {
		req.Status = "healthy"
	}

	// 创建代理配置
	proxy := &entity.Proxy{
		Name:     req.Name,
		Hosts:    req.Hosts,
		Port:     req.Port,
		Username: req.Username,
		Password: req.Password,
		Group:    req.Group,
		Status:   req.Status,
	}

	if err := h.svc.AddProxy(proxy); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "代理配置创建成功",
		"data":    proxy,
	})
}

// GetProxy 获取指定代理配置
func (h *Handler) GetProxy(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "invalid proxy id",
		})
		return
	}

	proxy, err := h.svc.GetProxy(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "proxy not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    proxy,
	})
}

// UpdateProxy 更新代理配置
func (h *Handler) UpdateProxy(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "invalid proxy id",
		})
		return
	}

	// 获取现有代理配置
	proxy, err := h.svc.GetProxy(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "proxy not found",
		})
		return
	}

	var req struct {
		Name     string `json:"name"`
		Hosts    string `json:"hosts"`
		Port     string `json:"port"`
		Username string `json:"username"`
		Password string `json:"password"`
		Group    string `json:"group"`
		Status   string `json:"status"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	// 更新字段
	if req.Name != "" {
		proxy.Name = req.Name
	}
	if req.Hosts != "" {
		proxy.Hosts = req.Hosts
	}
	if req.Port != "" {
		proxy.Port = req.Port
	}
	proxy.Username = req.Username
	proxy.Password = req.Password
	proxy.Group = req.Group
	if req.Status != "" {
		proxy.Status = req.Status
	}

	if err := h.svc.UpdateProxy(proxy); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "代理配置更新成功",
		"data":    proxy,
	})
}

// DeleteProxy 删除代理配置
func (h *Handler) DeleteProxy(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "invalid proxy id",
		})
		return
	}

	if err := h.svc.DeleteProxy(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "代理配置删除成功",
	})
}

// ListProxiesByGroup 获取指定分组的代理配置
func (h *Handler) ListProxiesByGroup(c *gin.Context) {
	group := c.Param("group")
	proxies, err := h.svc.ListProxiesByGroup(group)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    proxies,
	})
}

// ListProxiesByStatus 获取指定状态的代理配置
func (h *Handler) ListProxiesByStatus(c *gin.Context) {
	status := c.Param("status")
	proxies, err := h.svc.ListProxiesByStatus(status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    proxies,
	})
}

// ===== 代理使用记录相关处理器 =====

// GetProxyUsageByProxy 获取指定代理的使用记录
func (h *Handler) GetProxyUsageByProxy(c *gin.Context) {
	proxyID, err := strconv.ParseUint(c.Param("proxy_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "invalid proxy id",
		})
		return
	}

	usages, err := h.svc.GetProxyUsageByProxy(uint(proxyID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    usages,
	})
}

// GetProxyUsageByBrowser 获取指定浏览器的代理使用记录
func (h *Handler) GetProxyUsageByBrowser(c *gin.Context) {
	browserID, err := strconv.ParseUint(c.Param("browser_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "invalid browser id",
		})
		return
	}

	usages, err := h.svc.GetProxyUsageByBrowser(uint(browserID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    usages,
	})
}
