package handler

import (
	"crypto/rand"
	"encoding/json"
	"net/http"
	"strconv"

	"web3-control/internal/models/entity"

	"github.com/gin-gonic/gin"
)

// ===== 客户端管理相关处理器 =====

// CreateClient 创建客户端配置
func (h *Handler) CreateClient(c *gin.Context) {
	var req struct {
		Name     string `json:"name" binding:"required"`
		Group    string `json:"group"`
		IsMaster bool   `json:"is_master"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	// 生成客户端ID（16位大写字母、数字、连字符）
	clientID := generateClientID()

	// 创建客户端配置
	client := &entity.Client{
		ClientID: clientID,
		Name:     req.Name,
		Group:    req.Group,
		IsMaster: req.IsMaster,
		Online:   0, // 新创建的客户端默认离线
	}

	if err := h.svc.AddClient(client); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "客户端配置创建成功",
		"data":    client,
	})
}

// UpdateClient 更新客户端配置
func (h *Handler) UpdateClient(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "invalid client id",
		})
		return
	}

	// 获取现有客户端配置
	clients, err := h.svc.ListClients()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	var client *entity.Client
	for _, c := range clients {
		if c.ID == uint(id) {
			client = c
			break
		}
	}

	if client == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "client not found",
		})
		return
	}

	var req struct {
		Name     string `json:"name"`
		Group    string `json:"group"`
		IsMaster bool   `json:"is_master"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	// 更新字段
	if req.Name != "" {
		client.Name = req.Name
	}
	client.Group = req.Group
	client.IsMaster = req.IsMaster

	if err := h.svc.UpdateClient(client); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "客户端配置更新成功",
		"data":    client,
	})
}

// GetClientDetail 获取客户端详细信息
func (h *Handler) GetClientDetail(c *gin.Context) {
	clientID := c.Param("id")

	client, err := h.svc.GetClient(clientID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "client not found",
		})
		return
	}

	// 获取客户端相关的浏览器配置
	browsers, _ := h.svc.ListBrowsersByClientID(clientID)

	// 获取在线状态（从连接管理器）
	onlineClients := connManager.GetOnlineClientConnections()
	isOnline := false
	for _, onlineClient := range onlineClients {
		if onlineClient.ClientID == clientID {
			isOnline = true
			break
		}
	}

	clientDetail := map[string]interface{}{
		"client":    client,
		"browsers":  browsers,
		"is_online": isOnline,
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    clientDetail,
	})
}

// ToggleClientStatus 切换客户端状态（强制上线/下线）
func (h *Handler) ToggleClientStatus(c *gin.Context) {
	clientID := c.Param("id")

	var req struct {
		Online bool `json:"online"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	client, err := h.svc.GetClient(clientID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "client not found",
		})
		return
	}

	// 更新在线状态
	if req.Online {
		client.Online = 1
	} else {
		client.Online = 0
	}

	if err := h.svc.UpdateClient(client); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	// 如果是强制下线，还需要断开WebSocket连接
	if !req.Online {
		// TODO: 实现强制断开连接的逻辑
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "客户端状态更新成功",
		"data":    client,
	})
}

// SendCommandToClient 向客户端发送命令
func (h *Handler) SendCommandToClient(c *gin.Context) {
	clientID := c.Param("id")

	var req struct {
		Command string                 `json:"command" binding:"required"`
		Data    map[string]interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	// 检查客户端是否存在
	_, err := h.svc.GetClient(clientID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "client not found",
		})
		return
	}

	// 构建命令消息
	cmdMsg := map[string]interface{}{
		"command": req.Command,
		"data":    req.Data,
	}

	// 发送命令到客户端
	if connManager != nil {
		cmdBytes, _ := json.Marshal(cmdMsg)
		err := connManager.SendToClient(clientID, cmdBytes)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "发送命令失败",
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "命令发送成功",
	})
}

// GetClientGroups 获取所有客户端分组
func (h *Handler) GetClientGroups(c *gin.Context) {
	clients, err := h.svc.ListClients()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	// 提取所有分组
	groupMap := make(map[string]int)
	for _, client := range clients {
		if client.Group != "" {
			groupMap[client.Group]++
		}
	}

	// 转换为数组格式
	groups := make([]map[string]interface{}, 0, len(groupMap))
	for group, count := range groupMap {
		groups = append(groups, map[string]interface{}{
			"name":  group,
			"count": count,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    groups,
	})
}

// generateClientID 生成16位客户端ID（大写字母、数字、连字符）
func generateClientID() string {
	// 定义字符集：大写字母、数字（连字符使用频率较低）
	letters := "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	numbers := "0123456789"

	// 生成16位随机字符
	b := make([]byte, 16)
	for i := range b {
		randomBytes := make([]byte, 1)
		rand.Read(randomBytes)
		randNum := int(randomBytes[0])

		// 80%概率使用字母，15%概率使用数字，5%概率使用连字符
		if randNum < 204 { // 80% (204/255)
			b[i] = letters[randNum%len(letters)]
		} else {
			b[i] = numbers[randNum%len(numbers)]
		}
	}

	return string(b)
}
