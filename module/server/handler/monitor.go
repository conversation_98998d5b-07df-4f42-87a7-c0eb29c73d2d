package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ===== 监控相关处理器 =====

// GetSystemStats 获取系统统计信息
func (h *Handler) GetSystemStats(c *gin.Context) {
	stats := h.svc.GetSystemStats()
	c.<PERSON>(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    stats,
	})
}

// GetPerformanceStats 获取性能统计信息
func (h *Handler) GetPerformanceStats(c *gin.Context) {
	stats := h.svc.GetPerformanceStats()
	c.JSO<PERSON>(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    stats,
	})
}

// GetHealthStatus 获取系统健康状态
func (h *Handler) GetHealthStatus(c *gin.Context) {
	status := h.svc.GetHealthStatus()
	c.<PERSON>(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    status,
	})
}

// GetDashboardData 获取仪表板数据
func (h *Handler) GetDashboardData(c *gin.Context) {
	systemStats := h.svc.GetSystemStats()
	performanceStats := h.svc.GetPerformanceStats()
	healthStatus := h.svc.GetHealthStatus()

	dashboardData := map[string]interface{}{
		"system":      systemStats,
		"performance": performanceStats,
		"health":      healthStatus,
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    dashboardData,
	})
}
