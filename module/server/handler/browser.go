package handler

import (
	"net/http"
	"strconv"

	"web3-control/internal/models/entity"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ===== 浏览器管理相关处理器 =====

// ListBrowsers 获取所有浏览器配置列表
func (h *Handler) ListBrowsers(c *gin.Context) {
	browsers, err := h.svc.ListBrowsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    browsers,
	})
}

// CreateBrowser 创建浏览器配置
func (h *Handler) CreateBrowser(c *gin.Context) {
	var req struct {
		Name       string `json:"name" binding:"required"`
		ClientID   string `json:"client_id" binding:"required"`
		Group      string `json:"group"`
		Proxy      string `json:"proxy"`
		UserDir    string `json:"user_dir"`
		Port       string `json:"port"`
		UserAgent  string `json:"user_agent"`
		WindowSize string `json:"window_size"`
		Headless   bool   `json:"headless"`
		DisableGPU bool   `json:"disable_gpu"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	// 生成浏览器ID
	browserID := uuid.New().String()

	// 创建浏览器配置
	browser := &entity.Browser{
		Name:       req.Name,
		BrowserID:  browserID,
		ClientID:   req.ClientID,
		Group:      req.Group,
		Proxy:      req.Proxy,
		UserDir:    req.UserDir,
		Port:       req.Port,
		UserAgent:  req.UserAgent,
		WindowSize: req.WindowSize,
		Headless:   req.Headless,
		DisableGPU: req.DisableGPU,
	}

	if err := h.svc.AddBrowser(browser); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "浏览器配置创建成功",
		"data":    browser,
	})
}

// GetBrowser 获取指定浏览器配置
func (h *Handler) GetBrowser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "invalid browser id",
		})
		return
	}

	browser, err := h.svc.GetBrowser(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "browser not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    browser,
	})
}

// UpdateBrowser 更新浏览器配置
func (h *Handler) UpdateBrowser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "invalid browser id",
		})
		return
	}

	// 获取现有浏览器配置
	browser, err := h.svc.GetBrowser(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "browser not found",
		})
		return
	}

	var req struct {
		Name       string `json:"name"`
		ClientID   string `json:"client_id"`
		Group      string `json:"group"`
		Proxy      string `json:"proxy"`
		UserDir    string `json:"user_dir"`
		Port       string `json:"port"`
		UserAgent  string `json:"user_agent"`
		WindowSize string `json:"window_size"`
		Headless   bool   `json:"headless"`
		DisableGPU bool   `json:"disable_gpu"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	// 更新字段
	if req.Name != "" {
		browser.Name = req.Name
	}
	if req.ClientID != "" {
		browser.ClientID = req.ClientID
	}
	browser.Group = req.Group
	browser.Proxy = req.Proxy
	browser.UserDir = req.UserDir
	browser.Port = req.Port
	browser.UserAgent = req.UserAgent
	browser.WindowSize = req.WindowSize
	browser.Headless = req.Headless
	browser.DisableGPU = req.DisableGPU

	if err := h.svc.UpdateBrowser(browser); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "浏览器配置更新成功",
		"data":    browser,
	})
}

// DeleteBrowser 删除浏览器配置
func (h *Handler) DeleteBrowser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "invalid browser id",
		})
		return
	}

	if err := h.svc.DeleteBrowser(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "浏览器配置删除成功",
	})
}

// ListBrowsersByGroup 获取指定分组的浏览器配置
func (h *Handler) ListBrowsersByGroup(c *gin.Context) {
	group := c.Param("group")
	browsers, err := h.svc.ListBrowsersByGroup(group)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    browsers,
	})
}

// ListBrowsersByClientID 获取指定客户端的浏览器配置
func (h *Handler) ListBrowsersByClientID(c *gin.Context) {
	clientID := c.Param("client_id")
	browsers, err := h.svc.ListBrowsersByClientID(clientID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    browsers,
	})
}

// ===== 浏览器控制相关处理器 =====

// LaunchBrowser 启动浏览器
func (h *Handler) LaunchBrowser(c *gin.Context) {
	var req struct {
		BrowserID string `json:"browser_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	if err := h.svc.LaunchBrowser(req.BrowserID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "浏览器启动命令已发送",
	})
}

// CloseBrowser 关闭浏览器
func (h *Handler) CloseBrowser(c *gin.Context) {
	var req struct {
		BrowserID string `json:"browser_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	if err := h.svc.CloseBrowser(req.BrowserID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "浏览器关闭命令已发送",
	})
}

// LaunchBrowsersByGroup 批量启动指定分组的浏览器
func (h *Handler) LaunchBrowsersByGroup(c *gin.Context) {
	var req struct {
		Group string `json:"group" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	if err := h.svc.LaunchBrowsersByGroup(req.Group); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "分组浏览器启动命令已发送",
	})
}

// CloseBrowsersByGroup 批量关闭指定分组的浏览器
func (h *Handler) CloseBrowsersByGroup(c *gin.Context) {
	var req struct {
		Group string `json:"group" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	if err := h.svc.CloseBrowsersByGroup(req.Group); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "分组浏览器关闭命令已发送",
	})
}

// ===== 浏览器状态相关处理器 =====

// ListBrowserStatuses 获取所有浏览器状态
func (h *Handler) ListBrowserStatuses(c *gin.Context) {
	statuses, err := h.svc.ListBrowserStatuses()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    statuses,
	})
}

// GetBrowserStatus 获取指定浏览器状态
func (h *Handler) GetBrowserStatus(c *gin.Context) {
	browserID := c.Param("browser_id")
	status, err := h.svc.GetBrowserStatus(browserID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "browser status not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    status,
	})
}
