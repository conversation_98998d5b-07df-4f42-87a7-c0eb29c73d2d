package service

import (
	"testing"

	"web3-control/internal/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockConnectionManager 模拟连接管理器
type MockConnectionManagerSimple struct {
	mock.Mock
}

func (m *MockConnectionManagerSimple) BroadcastToGroup(group string, message []byte) {
	m.Called(group, message)
}

func (m *MockConnectionManagerSimple) BroadcastToSlaves(message []byte) {
	m.Called(message)
}

func (m *MockConnectionManagerSimple) SendToClient(clientID string, message []byte) error {
	args := m.Called(clientID, message)
	return args.Error(0)
}

func (m *MockConnectionManagerSimple) GetOnlineClients() []interface{} {
	args := m.Called()
	return args.Get(0).([]interface{})
}

// setupTestServiceSimple 设置简单测试服务（不使用数据库）
func setupTestServiceSimple() *Service {
	cfg := &config.Config{
		Server: struct {
			Addr string `yaml:"addr"`
			Auth string `yaml:"auth"`
		}{
			Addr: ":8080",
			Auth: "test-auth",
		},
	}

	svc := &Service{
		cfg: cfg,
		db:  nil, // 不使用数据库
	}
	svc.recordingManager = NewRecordingManager(svc)
	return svc
}

func TestService_ValidateAuth_Simple(t *testing.T) {
	svc := setupTestServiceSimple()

	// 测试正确的认证
	assert.True(t, svc.ValidateAuth("test-auth"))

	// 测试错误的认证
	assert.False(t, svc.ValidateAuth("wrong-auth"))
}

func TestRecordingManager_StartStop_Simple(t *testing.T) {
	svc := setupTestServiceSimple()
	rm := svc.recordingManager

	// 测试初始状态
	assert.False(t, rm.IsRecording())

	// 测试开始录制
	err := rm.StartRecording("Test Recording", "Test Description")
	assert.NoError(t, err)
	assert.True(t, rm.IsRecording())

	// 测试当前录制信息
	currentRecording := rm.GetCurrentRecording()
	assert.NotNil(t, currentRecording)
	assert.Equal(t, "Test Recording", currentRecording.Name)
	assert.Equal(t, "Test Description", currentRecording.Description)

	// 测试重复开始录制应该失败
	err = rm.StartRecording("Another Recording", "Another Description")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "录制已在进行中")

	// 测试添加事件
	eventData := map[string]interface{}{
		"type": "click",
		"x":    100,
		"y":    200,
	}
	err = rm.AddEvent("click", eventData)
	assert.NoError(t, err)

	// 测试停止录制
	err = rm.StopRecording()
	assert.NoError(t, err)
	assert.False(t, rm.IsRecording())

	// 测试停止未开始的录制应该失败
	err = rm.StopRecording()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "当前没有进行录制")
}

func TestRecordingManager_AddEvent_NotRecording(t *testing.T) {
	svc := setupTestServiceSimple()
	rm := svc.recordingManager

	// 在没有录制的情况下添加事件应该被忽略
	eventData := map[string]interface{}{
		"type": "click",
		"x":    100,
		"y":    200,
	}
	err := rm.AddEvent("click", eventData)
	assert.NoError(t, err) // 应该不报错，但事件被忽略
}

func TestRecordingManager_SetConnectionManager(t *testing.T) {
	svc := setupTestServiceSimple()
	rm := svc.recordingManager
	mockConnManager := &MockConnectionManagerSimple{}

	// 设置连接管理器
	rm.SetConnectionManager(mockConnManager)
	assert.Equal(t, mockConnManager, rm.connManager)
}

func TestService_SetConnectionManager(t *testing.T) {
	svc := setupTestServiceSimple()
	mockConnManager := &MockConnectionManagerSimple{}

	// 通过Service设置连接管理器
	svc.SetConnectionManager(mockConnManager)
	assert.Equal(t, mockConnManager, svc.recordingManager.connManager)
}

func TestService_RecordingMethods(t *testing.T) {
	svc := setupTestServiceSimple()

	// 测试Service层的录制方法
	assert.False(t, svc.IsRecording())

	err := svc.StartRecording("Test Recording", "Test Description")
	assert.NoError(t, err)
	assert.True(t, svc.IsRecording())

	eventData := map[string]interface{}{
		"type":  "input",
		"value": "test",
	}
	err = svc.AddEventToRecording("input", eventData)
	assert.NoError(t, err)

	err = svc.StopRecording()
	assert.NoError(t, err)
	assert.False(t, svc.IsRecording())
}

func TestRecordingManager_PlayRecording_WithMockConnManager(t *testing.T) {
	svc := setupTestServiceSimple()
	rm := svc.recordingManager
	mockConnManager := &MockConnectionManagerSimple{}
	rm.SetConnectionManager(mockConnManager)

	// 模拟播放录制（不涉及数据库）
	// 这里主要测试方法调用不会panic
	err := rm.PlayRecording(1, "test-group", []string{"client1", "client2"})
	// 由于没有数据库，这里会返回错误，但不应该panic
	assert.Error(t, err)
}

// 基准测试
func BenchmarkRecordingManager_AddEvent(b *testing.B) {
	svc := setupTestServiceSimple()
	rm := svc.recordingManager

	rm.StartRecording("Benchmark Recording", "Benchmark Description")

	eventData := map[string]interface{}{
		"type": "click",
		"x":    100,
		"y":    200,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		rm.AddEvent("click", eventData)
	}
}

func BenchmarkService_ValidateAuth(b *testing.B) {
	svc := setupTestServiceSimple()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		svc.ValidateAuth("test-auth")
	}
}
