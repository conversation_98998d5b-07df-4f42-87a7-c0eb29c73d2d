package service

import (
	"runtime"
	"sync"
	"time"
)

// SystemStats 系统统计信息
type SystemStats struct {
	// 系统信息
	CPUUsage       float64 `json:"cpu_usage"`
	MemoryUsage    float64 `json:"memory_usage"`
	GoroutineCount int     `json:"goroutine_count"`

	// 连接统计
	TotalConnections   int `json:"total_connections"`
	OnlineConnections  int `json:"online_connections"`
	OfflineConnections int `json:"offline_connections"`

	// 浏览器统计
	TotalBrowsers   int `json:"total_browsers"`
	RunningBrowsers int `json:"running_browsers"`
	StoppedBrowsers int `json:"stopped_browsers"`

	// 代理统计
	TotalProxies     int `json:"total_proxies"`
	HealthyProxies   int `json:"healthy_proxies"`
	UnhealthyProxies int `json:"unhealthy_proxies"`
	DisabledProxies  int `json:"disabled_proxies"`

	// 录制统计
	TotalRecordings  int `json:"total_recordings"`
	ActiveRecordings int `json:"active_recordings"`

	// 时间戳
	Timestamp time.Time `json:"timestamp"`
}

// PerformanceStats 性能统计信息
type PerformanceStats struct {
	// API请求统计
	TotalRequests   int64   `json:"total_requests"`
	SuccessRequests int64   `json:"success_requests"`
	ErrorRequests   int64   `json:"error_requests"`
	AvgResponseTime float64 `json:"avg_response_time"`

	// WebSocket统计
	WSConnections int64 `json:"ws_connections"`
	WSMessages    int64 `json:"ws_messages"`
	WSErrors      int64 `json:"ws_errors"`

	// 数据库统计
	DBConnections int   `json:"db_connections"`
	DBQueries     int64 `json:"db_queries"`
	DBErrors      int64 `json:"db_errors"`

	// 时间戳
	Timestamp time.Time `json:"timestamp"`
}

// MonitorService 监控服务
type MonitorService struct {
	svc         *Service
	stats       *SystemStats
	performance *PerformanceStats
	mutex       sync.RWMutex

	// 性能计数器
	requestCounter   int64
	successCounter   int64
	errorCounter     int64
	responseTimeSum  float64
	wsConnCounter    int64
	wsMessageCounter int64
	wsErrorCounter   int64
	dbQueryCounter   int64
	dbErrorCounter   int64
}

// NewMonitorService 创建监控服务
func NewMonitorService(svc *Service) *MonitorService {
	ms := &MonitorService{
		svc:         svc,
		stats:       &SystemStats{},
		performance: &PerformanceStats{},
	}

	// 启动监控协程
	go ms.startMonitoring()

	return ms
}

// startMonitoring 启动监控
func (ms *MonitorService) startMonitoring() {
	ticker := time.NewTicker(5 * time.Second) // 每5秒更新一次
	defer ticker.Stop()

	for range ticker.C {
		ms.updateStats()
	}
}

// updateStats 更新统计信息
func (ms *MonitorService) updateStats() {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	// 更新系统统计
	ms.updateSystemStats()

	// 更新性能统计
	ms.updatePerformanceStats()
}

// updateSystemStats 更新系统统计
func (ms *MonitorService) updateSystemStats() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 系统信息
	ms.stats.CPUUsage = ms.getCPUUsage()
	ms.stats.MemoryUsage = float64(m.Alloc) / 1024 / 1024 // MB
	ms.stats.GoroutineCount = runtime.NumGoroutine()

	// 连接统计
	if ms.svc.db != nil {
		clients, _ := ms.svc.ListClients()
		ms.stats.TotalConnections = len(clients)

		onlineCount := 0
		for _, client := range clients {
			if client.Online == 1 {
				onlineCount++
			}
		}
		ms.stats.OnlineConnections = onlineCount
		ms.stats.OfflineConnections = ms.stats.TotalConnections - onlineCount

		// 浏览器统计
		browsers, _ := ms.svc.ListBrowsers()
		ms.stats.TotalBrowsers = len(browsers)
		// TODO: 实现运行状态统计
		ms.stats.RunningBrowsers = 0
		ms.stats.StoppedBrowsers = ms.stats.TotalBrowsers

		// 代理统计
		proxies, _ := ms.svc.ListProxies()
		ms.stats.TotalProxies = len(proxies)

		healthyCount := 0
		unhealthyCount := 0
		disabledCount := 0
		for _, proxy := range proxies {
			switch proxy.Status {
			case "healthy":
				healthyCount++
			case "unhealthy":
				unhealthyCount++
			case "disabled":
				disabledCount++
			}
		}
		ms.stats.HealthyProxies = healthyCount
		ms.stats.UnhealthyProxies = unhealthyCount
		ms.stats.DisabledProxies = disabledCount

		// 录制统计
		recordings, _ := ms.svc.ListRecordings()
		ms.stats.TotalRecordings = len(recordings)
		ms.stats.ActiveRecordings = 0
		if ms.svc.recordingManager.IsRecording() {
			ms.stats.ActiveRecordings = 1
		}
	}

	ms.stats.Timestamp = time.Now()
}

// updatePerformanceStats 更新性能统计
func (ms *MonitorService) updatePerformanceStats() {
	ms.performance.TotalRequests = ms.requestCounter
	ms.performance.SuccessRequests = ms.successCounter
	ms.performance.ErrorRequests = ms.errorCounter

	if ms.requestCounter > 0 {
		ms.performance.AvgResponseTime = ms.responseTimeSum / float64(ms.requestCounter)
	}

	ms.performance.WSConnections = ms.wsConnCounter
	ms.performance.WSMessages = ms.wsMessageCounter
	ms.performance.WSErrors = ms.wsErrorCounter
	ms.performance.DBQueries = ms.dbQueryCounter
	ms.performance.DBErrors = ms.dbErrorCounter

	// 数据库连接数
	if ms.svc.db != nil {
		if sqlDB, err := ms.svc.db.DB(); err == nil {
			ms.performance.DBConnections = sqlDB.Stats().OpenConnections
		}
	}

	ms.performance.Timestamp = time.Now()
}

// getCPUUsage 获取CPU使用率（简化实现）
func (ms *MonitorService) getCPUUsage() float64 {
	// 这里是一个简化的CPU使用率计算
	// 在生产环境中，建议使用更精确的CPU监控库
	return float64(runtime.NumGoroutine()) / 100.0
}

// GetSystemStats 获取系统统计信息
func (ms *MonitorService) GetSystemStats() *SystemStats {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	// 返回副本
	stats := *ms.stats
	return &stats
}

// GetPerformanceStats 获取性能统计信息
func (ms *MonitorService) GetPerformanceStats() *PerformanceStats {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	// 返回副本
	perf := *ms.performance
	return &perf
}

// RecordRequest 记录API请求
func (ms *MonitorService) RecordRequest(success bool, responseTime float64) {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	ms.requestCounter++
	ms.responseTimeSum += responseTime

	if success {
		ms.successCounter++
	} else {
		ms.errorCounter++
	}
}

// RecordWSConnection 记录WebSocket连接
func (ms *MonitorService) RecordWSConnection() {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	ms.wsConnCounter++
}

// RecordWSMessage 记录WebSocket消息
func (ms *MonitorService) RecordWSMessage() {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	ms.wsMessageCounter++
}

// RecordWSError 记录WebSocket错误
func (ms *MonitorService) RecordWSError() {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	ms.wsErrorCounter++
}

// RecordDBQuery 记录数据库查询
func (ms *MonitorService) RecordDBQuery() {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	ms.dbQueryCounter++
}

// RecordDBError 记录数据库错误
func (ms *MonitorService) RecordDBError() {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	ms.dbErrorCounter++
}

// GetHealthStatus 获取系统健康状态
func (ms *MonitorService) GetHealthStatus() map[string]interface{} {
	stats := ms.GetSystemStats()

	status := "healthy"
	issues := []string{}

	// 检查内存使用
	if stats.MemoryUsage > 1024 { // 超过1GB
		status = "warning"
		issues = append(issues, "高内存使用")
	}

	// 检查协程数量
	if stats.GoroutineCount > 1000 {
		status = "warning"
		issues = append(issues, "协程数量过多")
	}

	// 检查连接状态
	if stats.OnlineConnections == 0 && stats.TotalConnections > 0 {
		status = "warning"
		issues = append(issues, "无在线连接")
	}

	return map[string]interface{}{
		"status": status,
		"issues": issues,
		"stats":  stats,
	}
}
