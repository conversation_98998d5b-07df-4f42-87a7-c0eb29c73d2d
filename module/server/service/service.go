package service

import (
	"encoding/json"
	"log"
	"sync"

	"web3-control/internal/config"
	"web3-control/internal/database"
	"web3-control/internal/models"
	"web3-control/internal/models/entity"

	"gorm.io/gorm"
)

// Service 服务层结构体
type Service struct {
	cfg              *config.Config
	db               *gorm.DB
	clients          sync.Map // 用于存储在线客户端
	recordingManager *RecordingManager
	monitorService   *MonitorService
}

// NewService 创建新的服务实例
func NewService(cfg *config.Config) *Service {
	svc := &Service{
		cfg: cfg,
		db:  database.GetDB(),
	}
	svc.recordingManager = NewRecordingManager(svc)
	svc.monitorService = NewMonitorService(svc)
	return svc
}

// SetConnectionManager 设置连接管理器
func (s *Service) SetConnectionManager(connManager models.ConnectionManager) {
	s.recordingManager.SetConnectionManager(connManager)
}

// AddClient 添加新的客户端
func (s *Service) AddClient(client *entity.Client) error {
	// 保存到数据库
	if err := s.db.Create(client).Error; err != nil {
		return err
	}
	// 添加到在线客户端列表
	s.clients.Store(client.ClientID, client)
	return nil
}

// RemoveClient 移除客户端
func (s *Service) RemoveClient(clientID string) error {
	// 从数据库中删除
	if err := s.db.Delete(&entity.Client{}, "client_id = ?", clientID).Error; err != nil {
		return err
	}
	// 从在线客户端列表中删除
	s.clients.Delete(clientID)
	return nil
}

// GetClient 获取客户端信息
func (s *Service) GetClient(clientID string) (*entity.Client, error) {
	var client entity.Client
	if err := s.db.First(&client, "client_id = ?", clientID).Error; err != nil {
		return nil, err
	}
	return &client, nil
}

// ListClients 获取所有客户端列表
func (s *Service) ListClients() ([]*entity.Client, error) {
	var clients []*entity.Client
	if err := s.db.Find(&clients).Error; err != nil {
		return nil, err
	}
	return clients, nil
}

// ListClientsByGroup 获取指定分组的所有客户端
func (s *Service) ListClientsByGroup(group string) ([]*entity.Client, error) {
	var clients []*entity.Client
	if err := s.db.Where("group = ?", group).Find(&clients).Error; err != nil {
		return nil, err
	}
	return clients, nil
}

// UpdateClient 更新客户端信息
func (s *Service) UpdateClient(client *entity.Client) error {
	return s.db.Save(client).Error
}

// ValidateAuth 验证认证密钥
func (s *Service) ValidateAuth(auth string) bool {
	return s.cfg.Server.Auth == auth
}

// AddRecording 添加录制
func (s *Service) AddRecording(recording *entity.Recording) error {
	return s.db.Create(recording).Error
}

// GetRecording 获取录制信息
func (s *Service) GetRecording(id uint) (*entity.Recording, error) {
	var recording entity.Recording
	if err := s.db.Preload("Events").First(&recording, id).Error; err != nil {
		return nil, err
	}
	return &recording, nil
}

// ListRecordings 获取所有录制列表
func (s *Service) ListRecordings() ([]*entity.Recording, error) {
	var recordings []*entity.Recording
	if err := s.db.Find(&recordings).Error; err != nil {
		return nil, err
	}
	return recordings, nil
}

// DeleteRecording 删除录制
func (s *Service) DeleteRecording(id uint) error {
	return s.db.Delete(&entity.Recording{}, id).Error
}

// StartRecording 开始录制
func (s *Service) StartRecording(name, description string) error {
	return s.recordingManager.StartRecording(name, description)
}

// StopRecording 停止录制
func (s *Service) StopRecording() error {
	return s.recordingManager.StopRecording()
}

// IsRecording 检查是否正在录制
func (s *Service) IsRecording() bool {
	return s.recordingManager.IsRecording()
}

// PlayRecording 播放录制
func (s *Service) PlayRecording(recordingID uint, targetGroup string, targetClients []string) error {
	return s.recordingManager.PlayRecording(recordingID, targetGroup, targetClients)
}

// AddEventToRecording 添加事件到当前录制
func (s *Service) AddEventToRecording(eventType string, data map[string]interface{}) error {
	return s.recordingManager.AddEvent(eventType, data)
}

// ===== 浏览器管理相关方法 =====

// AddBrowser 添加浏览器配置
func (s *Service) AddBrowser(browser *entity.Browser) error {
	return s.db.Create(browser).Error
}

// GetBrowser 获取浏览器配置
func (s *Service) GetBrowser(id uint) (*entity.Browser, error) {
	var browser entity.Browser
	if err := s.db.First(&browser, id).Error; err != nil {
		return nil, err
	}
	return &browser, nil
}

// GetBrowserByBrowserID 根据browser_id获取浏览器配置
func (s *Service) GetBrowserByBrowserID(browserID string) (*entity.Browser, error) {
	var browser entity.Browser
	if err := s.db.Where("browser_id = ?", browserID).First(&browser).Error; err != nil {
		return nil, err
	}
	return &browser, nil
}

// ListBrowsers 获取所有浏览器配置列表
func (s *Service) ListBrowsers() ([]*entity.Browser, error) {
	var browsers []*entity.Browser
	if err := s.db.Find(&browsers).Error; err != nil {
		return nil, err
	}
	return browsers, nil
}

// ListBrowsersByGroup 获取指定分组的浏览器配置
func (s *Service) ListBrowsersByGroup(group string) ([]*entity.Browser, error) {
	var browsers []*entity.Browser
	if err := s.db.Where("group = ?", group).Find(&browsers).Error; err != nil {
		return nil, err
	}
	return browsers, nil
}

// ListBrowsersByClientID 获取指定客户端的浏览器配置
func (s *Service) ListBrowsersByClientID(clientID string) ([]*entity.Browser, error) {
	var browsers []*entity.Browser
	if err := s.db.Where("client_id = ?", clientID).Find(&browsers).Error; err != nil {
		return nil, err
	}
	return browsers, nil
}

// UpdateBrowser 更新浏览器配置
func (s *Service) UpdateBrowser(browser *entity.Browser) error {
	return s.db.Save(browser).Error
}

// DeleteBrowser 删除浏览器配置
func (s *Service) DeleteBrowser(id uint) error {
	return s.db.Delete(&entity.Browser{}, id).Error
}

// GetBrowserStatus 获取浏览器状态
func (s *Service) GetBrowserStatus(browserID string) (*entity.BrowserStatus, error) {
	var status entity.BrowserStatus
	if err := s.db.Where("browser_id = ?", browserID).First(&status).Error; err != nil {
		return nil, err
	}
	return &status, nil
}

// UpdateBrowserStatus 更新浏览器状态
func (s *Service) UpdateBrowserStatus(status *entity.BrowserStatus) error {
	return s.db.Save(status).Error
}

// ListBrowserStatuses 获取所有浏览器状态
func (s *Service) ListBrowserStatuses() ([]*entity.BrowserStatus, error) {
	var statuses []*entity.BrowserStatus
	if err := s.db.Find(&statuses).Error; err != nil {
		return nil, err
	}
	return statuses, nil
}

// ===== 代理管理相关方法 =====

// AddProxy 添加代理配置
func (s *Service) AddProxy(proxy *entity.Proxy) error {
	return s.db.Create(proxy).Error
}

// GetProxy 获取代理配置
func (s *Service) GetProxy(id uint) (*entity.Proxy, error) {
	var proxy entity.Proxy
	if err := s.db.First(&proxy, id).Error; err != nil {
		return nil, err
	}
	return &proxy, nil
}

// ListProxies 获取所有代理配置列表
func (s *Service) ListProxies() ([]*entity.Proxy, error) {
	var proxies []*entity.Proxy
	if err := s.db.Find(&proxies).Error; err != nil {
		return nil, err
	}
	return proxies, nil
}

// ListProxiesByGroup 获取指定分组的代理配置
func (s *Service) ListProxiesByGroup(group string) ([]*entity.Proxy, error) {
	var proxies []*entity.Proxy
	if err := s.db.Where("group = ?", group).Find(&proxies).Error; err != nil {
		return nil, err
	}
	return proxies, nil
}

// ListProxiesByStatus 获取指定状态的代理配置
func (s *Service) ListProxiesByStatus(status string) ([]*entity.Proxy, error) {
	var proxies []*entity.Proxy
	if err := s.db.Where("status = ?", status).Find(&proxies).Error; err != nil {
		return nil, err
	}
	return proxies, nil
}

// UpdateProxy 更新代理配置
func (s *Service) UpdateProxy(proxy *entity.Proxy) error {
	return s.db.Save(proxy).Error
}

// DeleteProxy 删除代理配置
func (s *Service) DeleteProxy(id uint) error {
	return s.db.Delete(&entity.Proxy{}, id).Error
}

// AddProxyUsage 添加代理使用记录
func (s *Service) AddProxyUsage(usage *entity.ProxyUsage) error {
	return s.db.Create(usage).Error
}

// GetProxyUsageByProxy 获取指定代理的使用记录
func (s *Service) GetProxyUsageByProxy(proxyID uint) ([]*entity.ProxyUsage, error) {
	var usages []*entity.ProxyUsage
	if err := s.db.Where("proxy_id = ?", proxyID).Find(&usages).Error; err != nil {
		return nil, err
	}
	return usages, nil
}

// GetProxyUsageByBrowser 获取指定浏览器的代理使用记录
func (s *Service) GetProxyUsageByBrowser(browserID uint) ([]*entity.ProxyUsage, error) {
	var usages []*entity.ProxyUsage
	if err := s.db.Where("browser_id = ?", browserID).Find(&usages).Error; err != nil {
		return nil, err
	}
	return usages, nil
}

// ===== 浏览器控制相关方法 =====

// LaunchBrowser 启动浏览器
func (s *Service) LaunchBrowser(browserID string) error {
	// 获取浏览器配置
	browser, err := s.GetBrowserByBrowserID(browserID)
	if err != nil {
		return err
	}

	// 获取绑定的客户端
	client, err := s.GetClient(browser.ClientID)
	if err != nil {
		return err
	}

	// 构建启动命令
	launchCmd := map[string]interface{}{
		"command":    "launch_browser",
		"browser_id": browser.BrowserID,
		"config": map[string]interface{}{
			"browser_id":  browser.BrowserID,
			"name":        browser.Name,
			"user_dir":    browser.UserDir,
			"proxy":       browser.Proxy,
			"port":        browser.Port,
			"user_agent":  browser.UserAgent,
			"window_size": browser.WindowSize,
			"headless":    browser.Headless,
			"disable_gpu": browser.DisableGPU,
		},
	}

	// 发送启动命令到客户端
	if s.recordingManager.connManager != nil {
		cmdBytes, _ := json.Marshal(launchCmd)
		return s.recordingManager.connManager.SendToClient(client.ClientID, cmdBytes)
	}

	return nil
}

// CloseBrowser 关闭浏览器
func (s *Service) CloseBrowser(browserID string) error {
	// 获取浏览器配置
	browser, err := s.GetBrowserByBrowserID(browserID)
	if err != nil {
		return err
	}

	// 获取绑定的客户端
	client, err := s.GetClient(browser.ClientID)
	if err != nil {
		return err
	}

	// 构建关闭命令
	closeCmd := map[string]interface{}{
		"command":    "close_browser",
		"browser_id": browser.BrowserID,
	}

	// 发送关闭命令到客户端
	if s.recordingManager.connManager != nil {
		cmdBytes, _ := json.Marshal(closeCmd)
		return s.recordingManager.connManager.SendToClient(client.ClientID, cmdBytes)
	}

	return nil
}

// LaunchBrowsersByGroup 批量启动指定分组的浏览器
func (s *Service) LaunchBrowsersByGroup(group string) error {
	browsers, err := s.ListBrowsersByGroup(group)
	if err != nil {
		return err
	}

	for _, browser := range browsers {
		if err := s.LaunchBrowser(browser.BrowserID); err != nil {
			log.Printf("启动浏览器 %s 失败: %v", browser.BrowserID, err)
		}
	}

	return nil
}

// CloseBrowsersByGroup 批量关闭指定分组的浏览器
func (s *Service) CloseBrowsersByGroup(group string) error {
	browsers, err := s.ListBrowsersByGroup(group)
	if err != nil {
		return err
	}

	for _, browser := range browsers {
		if err := s.CloseBrowser(browser.BrowserID); err != nil {
			log.Printf("关闭浏览器 %s 失败: %v", browser.BrowserID, err)
		}
	}

	return nil
}

// ===== 监控相关方法 =====

// GetSystemStats 获取系统统计信息
func (s *Service) GetSystemStats() *SystemStats {
	return s.monitorService.GetSystemStats()
}

// GetPerformanceStats 获取性能统计信息
func (s *Service) GetPerformanceStats() *PerformanceStats {
	return s.monitorService.GetPerformanceStats()
}

// GetHealthStatus 获取系统健康状态
func (s *Service) GetHealthStatus() map[string]interface{} {
	return s.monitorService.GetHealthStatus()
}

// RecordRequest 记录API请求
func (s *Service) RecordRequest(success bool, responseTime float64) {
	s.monitorService.RecordRequest(success, responseTime)
}