package service

import (
	"testing"

	"web3-control/internal/config"
	"web3-control/internal/models/entity"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// MockConnectionManager 模拟连接管理器
type MockConnectionManager struct {
	mock.Mock
}

func (m *MockConnectionManager) BroadcastToGroup(group string, message []byte) {
	m.Called(group, message)
}

func (m *MockConnectionManager) BroadcastToSlaves(message []byte) {
	m.Called(message)
}

func (m *MockConnectionManager) SendToClient(clientID string, message []byte) error {
	args := m.Called(clientID, message)
	return args.Error(0)
}

func (m *MockConnectionManager) GetOnlineClients() []interface{} {
	args := m.Called()
	return args.Get(0).([]interface{})
}

// setupTestDB 设置测试数据库
func setupTestDB() *gorm.DB {
	// 使用SQLite内存数据库进行测试
	db, err := gorm.Open(sqlite.Open("file::memory:?cache=shared"), &gorm.Config{})
	if err != nil {
		// 如果SQLite不可用，跳过测试
		panic("SQLite not available, skipping database tests")
	}

	// 自动迁移测试表
	err = db.AutoMigrate(&entity.Client{}, &entity.Recording{}, &entity.Event{})
	if err != nil {
		panic("failed to migrate database")
	}
	return db
}

// setupTestService 设置测试服务
func setupTestService() *Service {
	cfg := &config.Config{
		Server: struct {
			Addr string `yaml:"addr"`
			Auth string `yaml:"auth"`
		}{
			Addr: ":8080",
			Auth: "test-auth",
		},
	}

	svc := &Service{
		cfg: cfg,
		db:  setupTestDB(),
	}
	svc.recordingManager = NewRecordingManager(svc)
	return svc
}

func TestService_AddClient(t *testing.T) {
	svc := setupTestService()

	client := &entity.Client{
		ClientID: "test-client-1",
		Name:     "Test Client",
		Group:    "test-group",
		IsMaster: false,
		Online:   1,
	}

	err := svc.AddClient(client)
	assert.NoError(t, err)

	// 验证客户端是否已保存到数据库
	var savedClient entity.Client
	err = svc.db.First(&savedClient, "client_id = ?", "test-client-1").Error
	assert.NoError(t, err)
	assert.Equal(t, "Test Client", savedClient.Name)
	assert.Equal(t, "test-group", savedClient.Group)
}

func TestService_GetClient(t *testing.T) {
	svc := setupTestService()

	// 先添加一个客户端
	client := &entity.Client{
		ClientID: "test-client-2",
		Name:     "Test Client 2",
		Group:    "test-group",
		IsMaster: true,
		Online:   1,
	}
	svc.AddClient(client)

	// 获取客户端
	retrievedClient, err := svc.GetClient("test-client-2")
	assert.NoError(t, err)
	assert.Equal(t, "Test Client 2", retrievedClient.Name)
	assert.True(t, retrievedClient.IsMaster)
}

func TestService_ListClients(t *testing.T) {
	svc := setupTestService()

	// 添加多个客户端
	clients := []*entity.Client{
		{ClientID: "client-1", Name: "Client 1", Group: "group1"},
		{ClientID: "client-2", Name: "Client 2", Group: "group1"},
		{ClientID: "client-3", Name: "Client 3", Group: "group2"},
	}

	for _, client := range clients {
		svc.AddClient(client)
	}

	// 获取所有客户端
	allClients, err := svc.ListClients()
	assert.NoError(t, err)
	assert.Len(t, allClients, 3)

	// 按分组获取客户端
	group1Clients, err := svc.ListClientsByGroup("group1")
	assert.NoError(t, err)
	assert.Len(t, group1Clients, 2)
}

func TestService_RemoveClient(t *testing.T) {
	svc := setupTestService()

	// 先添加一个客户端
	client := &entity.Client{
		ClientID: "test-client-remove",
		Name:     "Test Client Remove",
		Group:    "test-group",
	}
	svc.AddClient(client)

	// 删除客户端
	err := svc.RemoveClient("test-client-remove")
	assert.NoError(t, err)

	// 验证客户端已被删除
	_, err = svc.GetClient("test-client-remove")
	assert.Error(t, err)
}

func TestService_ValidateAuth(t *testing.T) {
	svc := setupTestService()

	// 测试正确的认证
	assert.True(t, svc.ValidateAuth("test-auth"))

	// 测试错误的认证
	assert.False(t, svc.ValidateAuth("wrong-auth"))
}

func TestService_Recording(t *testing.T) {
	svc := setupTestService()

	// 测试开始录制
	err := svc.StartRecording("Test Recording", "Test Description")
	assert.NoError(t, err)
	assert.True(t, svc.IsRecording())

	// 测试重复开始录制应该失败
	err = svc.StartRecording("Another Recording", "Another Description")
	assert.Error(t, err)

	// 测试停止录制
	err = svc.StopRecording()
	assert.NoError(t, err)
	assert.False(t, svc.IsRecording())

	// 测试停止未开始的录制应该失败
	err = svc.StopRecording()
	assert.Error(t, err)
}

func TestService_AddRecording(t *testing.T) {
	svc := setupTestService()

	recording := &entity.Recording{
		Name:        "Test Recording",
		Description: "Test Description",
	}

	err := svc.AddRecording(recording)
	assert.NoError(t, err)
	assert.NotZero(t, recording.ID)
}

func TestService_GetRecording(t *testing.T) {
	svc := setupTestService()

	// 先添加一个录制
	recording := &entity.Recording{
		Name:        "Test Recording",
		Description: "Test Description",
	}
	svc.AddRecording(recording)

	// 获取录制
	retrievedRecording, err := svc.GetRecording(recording.ID)
	assert.NoError(t, err)
	assert.Equal(t, "Test Recording", retrievedRecording.Name)
}

func TestService_ListRecordings(t *testing.T) {
	svc := setupTestService()

	// 添加多个录制
	recordings := []*entity.Recording{
		{Name: "Recording 1", Description: "Description 1"},
		{Name: "Recording 2", Description: "Description 2"},
	}

	for _, recording := range recordings {
		svc.AddRecording(recording)
	}

	// 获取所有录制
	allRecordings, err := svc.ListRecordings()
	assert.NoError(t, err)
	assert.Len(t, allRecordings, 2)
}

func TestService_DeleteRecording(t *testing.T) {
	svc := setupTestService()

	// 先添加一个录制
	recording := &entity.Recording{
		Name:        "Test Recording Delete",
		Description: "Test Description",
	}
	svc.AddRecording(recording)

	// 删除录制
	err := svc.DeleteRecording(recording.ID)
	assert.NoError(t, err)

	// 验证录制已被删除
	_, err = svc.GetRecording(recording.ID)
	assert.Error(t, err)
}

func TestRecordingManager_AddEvent(t *testing.T) {
	svc := setupTestService()
	mockConnManager := &MockConnectionManager{}
	svc.SetConnectionManager(mockConnManager)

	// 开始录制
	err := svc.StartRecording("Test Recording", "Test Description")
	assert.NoError(t, err)

	// 添加事件
	eventData := map[string]interface{}{
		"type": "click",
		"x":    100,
		"y":    200,
	}

	err = svc.AddEventToRecording("click", eventData)
	assert.NoError(t, err)

	// 停止录制
	err = svc.StopRecording()
	assert.NoError(t, err)

	// 验证事件已保存
	recordings, err := svc.ListRecordings()
	assert.NoError(t, err)
	assert.Len(t, recordings, 1)

	recording, err := svc.GetRecording(recordings[0].ID)
	assert.NoError(t, err)
	assert.Len(t, recording.Events, 1)
	assert.Equal(t, "click", recording.Events[0].Type)
}
