<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">{{ .title }}</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showTab('dashboard')">监控仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showTab('clients')">客户端管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showTab('browsers')">浏览器管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showTab('proxies')">代理管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showTab('recordings')">录制管理</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 监控仪表板标签页 -->
        <div id="dashboard-tab" class="tab-content" style="display: none;">
            <div class="row">
                <!-- 系统概览 -->
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">系统状态</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <small class="text-muted">CPU使用率</small>
                                <div class="progress mb-1" style="height: 6px;">
                                    <div class="progress-bar" id="cpu-progress" style="width: 0%"></div>
                                </div>
                                <span id="cpu-text" class="small">0%</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">内存使用</small>
                                <div class="progress mb-1" style="height: 6px;">
                                    <div class="progress-bar bg-warning" id="memory-progress" style="width: 0%"></div>
                                </div>
                                <span id="memory-text" class="small">0 MB</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">协程数量</small>
                                <div><span id="goroutine-count" class="h6">0</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 连接统计 -->
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">连接统计</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="h4 mb-0 text-success" id="online-clients">0</div>
                                    <small class="text-muted">在线</small>
                                </div>
                                <div class="col-6">
                                    <div class="h4 mb-0 text-secondary" id="offline-clients">0</div>
                                    <small class="text-muted">离线</small>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                <div class="h5 mb-0" id="total-clients">0</div>
                                <small class="text-muted">总客户端</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 浏览器统计 -->
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">浏览器统计</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="h4 mb-0 text-success" id="running-browsers">0</div>
                                    <small class="text-muted">运行中</small>
                                </div>
                                <div class="col-6">
                                    <div class="h4 mb-0 text-secondary" id="stopped-browsers">0</div>
                                    <small class="text-muted">已停止</small>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                <div class="h5 mb-0" id="total-browsers">0</div>
                                <small class="text-muted">总浏览器</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 代理统计 -->
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">代理统计</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="h6 mb-0 text-success" id="healthy-proxies">0</div>
                                    <small class="text-muted">健康</small>
                                </div>
                                <div class="col-4">
                                    <div class="h6 mb-0 text-danger" id="unhealthy-proxies">0</div>
                                    <small class="text-muted">异常</small>
                                </div>
                                <div class="col-4">
                                    <div class="h6 mb-0 text-secondary" id="disabled-proxies">0</div>
                                    <small class="text-muted">禁用</small>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                <div class="h5 mb-0" id="total-proxies">0</div>
                                <small class="text-muted">总代理</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <!-- 性能图表 -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">性能监控</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="performanceChart" height="100"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 系统健康状态 -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">系统健康</h6>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <div class="h3 mb-2">
                                    <span id="health-status" class="badge bg-success">健康</span>
                                </div>
                                <div id="health-issues" class="small text-muted">
                                    系统运行正常
                                </div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">API请求统计</small>
                                <div class="d-flex justify-content-between">
                                    <span>总请求:</span>
                                    <span id="total-requests">0</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>成功率:</span>
                                    <span id="success-rate">0%</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>平均响应:</span>
                                    <span id="avg-response">0ms</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 客户端管理标签页 -->
        <div id="clients-tab" class="tab-content">
            <div class="row">
                <!-- 客户端列表 -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">客户端列表</h5>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshClients()">刷新</button>
                                <button class="btn btn-sm btn-success" onclick="showCreateClientModal()">新建客户端</button>
                                <button class="btn btn-sm btn-outline-success" onclick="startRecording()">开始录制</button>
                                <button class="btn btn-sm btn-outline-danger" onclick="stopRecording()">停止录制</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>名称</th>
                                            <th>分组</th>
                                            <th>类型</th>
                                            <th>状态</th>
                                            <th>最后在线</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="clientList">
                                        <!-- 客户端列表将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 控制面板 -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">控制面板</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">选择分组</label>
                                <select class="form-select" id="groupSelect">
                                    <option value="">全部</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">录制列表</label>
                                <select class="form-select" id="recordingSelect">
                                    <option value="">选择录制...</option>
                                </select>
                            </div>
                            <button class="btn btn-primary w-100" onclick="playRecording()">播放录制</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 浏览器管理标签页 -->
        <div id="browsers-tab" class="tab-content" style="display: none;">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">浏览器配置列表</h5>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshBrowsers()">刷新</button>
                                <button class="btn btn-sm btn-success" onclick="showCreateBrowserModal()">新建浏览器</button>
                                <button class="btn btn-sm btn-warning" onclick="launchSelectedBrowsers()">启动选中</button>
                                <button class="btn btn-sm btn-danger" onclick="closeSelectedBrowsers()">关闭选中</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th><input type="checkbox" id="selectAllBrowsers" onchange="toggleAllBrowsers()"></th>
                                            <th>名称</th>
                                            <th>客户端</th>
                                            <th>分组</th>
                                            <th>代理</th>
                                            <th>端口</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="browserList">
                                        <!-- 浏览器列表将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">浏览器控制</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">按分组操作</label>
                                <select class="form-select" id="browserGroupSelect">
                                    <option value="">选择分组...</option>
                                </select>
                            </div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-success" onclick="launchBrowsersByGroup()">启动分组浏览器</button>
                                <button class="btn btn-danger" onclick="closeBrowsersByGroup()">关闭分组浏览器</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 代理管理标签页 -->
        <div id="proxies-tab" class="tab-content" style="display: none;">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">代理配置列表</h5>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshProxies()">刷新</button>
                                <button class="btn btn-sm btn-success" onclick="showCreateProxyModal()">新建代理</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>名称</th>
                                            <th>主机</th>
                                            <th>端口</th>
                                            <th>分组</th>
                                            <th>状态</th>
                                            <th>成功/失败</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="proxyList">
                                        <!-- 代理列表将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">代理统计</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">按状态筛选</label>
                                <select class="form-select" id="proxyStatusFilter" onchange="filterProxiesByStatus()">
                                    <option value="">全部状态</option>
                                    <option value="healthy">健康</option>
                                    <option value="unhealthy">不健康</option>
                                    <option value="disabled">已禁用</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">按分组筛选</label>
                                <select class="form-select" id="proxyGroupFilter" onchange="filterProxiesByGroup()">
                                    <option value="">全部分组</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 录制管理标签页 -->
        <div id="recordings-tab" class="tab-content" style="display: none;">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">录制列表</h5>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshRecordings()">刷新</button>
                                <button class="btn btn-sm btn-outline-success" onclick="startRecording()">开始录制</button>
                                <button class="btn btn-sm btn-outline-danger" onclick="stopRecording()">停止录制</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>名称</th>
                                            <th>描述</th>
                                            <th>事件数量</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recordingList">
                                        <!-- 录制列表将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">录制控制</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">选择录制</label>
                                <select class="form-select" id="playRecordingSelect">
                                    <option value="">选择录制...</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">目标分组</label>
                                <select class="form-select" id="playGroupSelect">
                                    <option value="">全部客户端</option>
                                </select>
                            </div>
                            <button class="btn btn-primary w-100" onclick="playSelectedRecording()">播放录制</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建浏览器模态框 -->
    <div class="modal fade" id="createBrowserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建浏览器配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createBrowserForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">浏览器名称 *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">绑定客户端 *</label>
                                    <select class="form-select" name="client_id" required>
                                        <option value="">选择客户端...</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">分组</label>
                                    <input type="text" class="form-control" name="group">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">代理</label>
                                    <select class="form-select" name="proxy">
                                        <option value="">不使用代理</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">用户数据目录</label>
                                    <input type="text" class="form-control" name="user_dir">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">调试端口</label>
                                    <input type="text" class="form-control" name="port" placeholder="9222">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">用户代理</label>
                                    <input type="text" class="form-control" name="user_agent">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">窗口大小</label>
                                    <input type="text" class="form-control" name="window_size" placeholder="1920x1080">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="headless">
                                    <label class="form-check-label">无头模式</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="disable_gpu">
                                    <label class="form-check-label">禁用GPU</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createBrowser()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建客户端模态框 -->
    <div class="modal fade" id="createClientModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建客户端配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createClientForm">
                        <div class="mb-3">
                            <label class="form-label">客户端名称 *</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">分组</label>
                            <input type="text" class="form-control" name="group" placeholder="例如：local, remote">
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="is_master">
                            <label class="form-check-label">设为主控客户端</label>
                            <div class="form-text">主控客户端用于录制操作，从控客户端用于重放操作</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createClient()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑客户端模态框 -->
    <div class="modal fade" id="editClientModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑客户端配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editClientForm">
                        <input type="hidden" name="client_id">
                        <div class="mb-3">
                            <label class="form-label">客户端名称 *</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">分组</label>
                            <input type="text" class="form-control" name="group">
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="is_master">
                            <label class="form-check-label">设为主控客户端</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateClient()">更新</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 客户端详情模态框 -->
    <div class="modal fade" id="clientDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">客户端详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="clientDetailContent">
                        <!-- 客户端详情内容将通过JavaScript动态加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑浏览器模态框 -->
    <div class="modal fade" id="editBrowserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑浏览器配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editBrowserForm">
                        <input type="hidden" name="browser_id">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">浏览器名称 *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">绑定客户端 *</label>
                                    <select class="form-select" name="client_id" required>
                                        <option value="">选择客户端...</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">分组</label>
                                    <input type="text" class="form-control" name="group">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">代理</label>
                                    <select class="form-select" name="proxy">
                                        <option value="">不使用代理</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">用户数据目录</label>
                                    <input type="text" class="form-control" name="user_dir">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">调试端口</label>
                                    <input type="text" class="form-control" name="port" placeholder="9222">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">用户代理</label>
                                    <input type="text" class="form-control" name="user_agent">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">窗口大小</label>
                                    <input type="text" class="form-control" name="window_size" placeholder="1920x1080">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="headless">
                                    <label class="form-check-label">无头模式</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="disable_gpu">
                                    <label class="form-check-label">禁用GPU</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateBrowser()">更新</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建代理模态框 -->
    <div class="modal fade" id="createProxyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建代理配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createProxyForm">
                        <div class="mb-3">
                            <label class="form-label">代理名称 *</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">主机地址 *</label>
                                    <input type="text" class="form-control" name="hosts" required placeholder="127.0.0.1">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">端口 *</label>
                                    <input type="text" class="form-control" name="port" required placeholder="8080">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">用户名</label>
                                    <input type="text" class="form-control" name="username">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">密码</label>
                                    <input type="password" class="form-control" name="password">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">分组</label>
                                    <input type="text" class="form-control" name="group">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">状态</label>
                                    <select class="form-select" name="status">
                                        <option value="healthy">健康</option>
                                        <option value="unhealthy">不健康</option>
                                        <option value="disabled">已禁用</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createProxy()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 先加载client.js，确保函数能正确覆盖 -->
    <script src="/static/js/client.js"></script>
    <script src="/static/js/proxy.js"></script>
    <script src="/static/js/dashboard.js"></script>
    <!-- main.js最后加载 -->
    <script src="/static/js/main.js"></script>
</body>
</html>