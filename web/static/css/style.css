/* 全局样式 */
body {
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

/* 卡片样式 */
.card {
    box-shadow: 0 1px 3px rgba(0,0,0,.1);
    margin-bottom: 1rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0,0,0,.125);
}

/* 表格样式 */
.table th {
    border-top: none;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

/* 按钮样式 */
.btn-group {
    gap: 0.5rem;
}

/* 表单样式 */
.form-select {
    border-radius: 0.25rem;
}

/* 导航链接样式 */
.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 0.375rem;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.35em 0.65em;
}

/* 标签页内容样式 */
.tab-content {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1.25rem;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

.modal-body {
    padding: 1.25rem;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
}

/* 表单增强样式 */
.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 按钮增强样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    margin-right: 0.25rem;
}

.btn-sm:last-child {
    margin-right: 0;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-online {
    background-color: #28a745;
}

.status-offline {
    background-color: #6c757d;
}

.status-error {
    background-color: #dc3545;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container {
        padding: 0.5rem;
    }

    .card {
        margin-bottom: 0.5rem;
    }

    .card-header .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .card-header .btn-group .btn {
        margin-bottom: 0.25rem;
        margin-right: 0;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-sm {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }
}