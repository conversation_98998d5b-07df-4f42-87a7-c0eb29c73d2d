// ===== 监控仪表板相关函数 =====

let performanceChart = null;
let dashboardInterval = null;

// 初始化监控仪表板
function initDashboard() {
    // 创建性能图表
    createPerformanceChart();
    
    // 开始定时刷新
    startDashboardRefresh();
    
    // 立即加载一次数据
    refreshDashboard();
}

// 创建性能图表
function createPerformanceChart() {
    const ctx = document.getElementById('performanceChart');
    if (!ctx) return;
    
    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'CPU使用率 (%)',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.1,
                    yAxisID: 'y'
                },
                {
                    label: '内存使用 (MB)',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.1,
                    yAxisID: 'y1'
                },
                {
                    label: '在线连接数',
                    data: [],
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.1,
                    yAxisID: 'y2'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'CPU (%)'
                    },
                    min: 0,
                    max: 100
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '内存 (MB)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                },
                y2: {
                    type: 'linear',
                    display: false,
                    min: 0
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: '系统性能实时监控'
                }
            }
        }
    });
}

// 开始仪表板刷新
function startDashboardRefresh() {
    // 清除现有定时器
    if (dashboardInterval) {
        clearInterval(dashboardInterval);
    }
    
    // 每5秒刷新一次
    dashboardInterval = setInterval(refreshDashboard, 5000);
}

// 停止仪表板刷新
function stopDashboardRefresh() {
    if (dashboardInterval) {
        clearInterval(dashboardInterval);
        dashboardInterval = null;
    }
}

// 刷新仪表板数据
async function refreshDashboard() {
    try {
        const response = await fetch('/api/monitor/dashboard');
        const result = await response.json();
        if (result.code === 0) {
            updateDashboardData(result.data);
        } else {
            console.error('获取仪表板数据失败:', result.message);
        }
    } catch (error) {
        console.error('网络错误:', error);
    }
}

// 更新仪表板数据
function updateDashboardData(data) {
    const { system, performance, health } = data;
    
    // 更新系统状态
    updateSystemStats(system);
    
    // 更新性能统计
    updatePerformanceStats(performance);
    
    // 更新健康状态
    updateHealthStatus(health);
    
    // 更新图表
    updatePerformanceChart(system);
}

// 更新系统统计
function updateSystemStats(stats) {
    // CPU使用率
    const cpuProgress = document.getElementById('cpu-progress');
    const cpuText = document.getElementById('cpu-text');
    if (cpuProgress && cpuText) {
        const cpuUsage = Math.min(stats.cpu_usage, 100);
        cpuProgress.style.width = cpuUsage + '%';
        cpuText.textContent = cpuUsage.toFixed(1) + '%';
        
        // 根据使用率改变颜色
        cpuProgress.className = 'progress-bar';
        if (cpuUsage > 80) {
            cpuProgress.classList.add('bg-danger');
        } else if (cpuUsage > 60) {
            cpuProgress.classList.add('bg-warning');
        } else {
            cpuProgress.classList.add('bg-success');
        }
    }
    
    // 内存使用
    const memoryProgress = document.getElementById('memory-progress');
    const memoryText = document.getElementById('memory-text');
    if (memoryProgress && memoryText) {
        const memoryUsage = stats.memory_usage;
        const memoryPercent = Math.min((memoryUsage / 1024) * 100, 100); // 假设最大1GB
        memoryProgress.style.width = memoryPercent + '%';
        memoryText.textContent = memoryUsage.toFixed(1) + ' MB';
    }
    
    // 协程数量
    const goroutineCount = document.getElementById('goroutine-count');
    if (goroutineCount) {
        goroutineCount.textContent = stats.goroutine_count;
    }
    
    // 连接统计
    const onlineClients = document.getElementById('online-clients');
    const offlineClients = document.getElementById('offline-clients');
    const totalClients = document.getElementById('total-clients');
    if (onlineClients) onlineClients.textContent = stats.online_connections;
    if (offlineClients) offlineClients.textContent = stats.offline_connections;
    if (totalClients) totalClients.textContent = stats.total_connections;
    
    // 浏览器统计
    const runningBrowsers = document.getElementById('running-browsers');
    const stoppedBrowsers = document.getElementById('stopped-browsers');
    const totalBrowsers = document.getElementById('total-browsers');
    if (runningBrowsers) runningBrowsers.textContent = stats.running_browsers;
    if (stoppedBrowsers) stoppedBrowsers.textContent = stats.stopped_browsers;
    if (totalBrowsers) totalBrowsers.textContent = stats.total_browsers;
    
    // 代理统计
    const healthyProxies = document.getElementById('healthy-proxies');
    const unhealthyProxies = document.getElementById('unhealthy-proxies');
    const disabledProxies = document.getElementById('disabled-proxies');
    const totalProxies = document.getElementById('total-proxies');
    if (healthyProxies) healthyProxies.textContent = stats.healthy_proxies;
    if (unhealthyProxies) unhealthyProxies.textContent = stats.unhealthy_proxies;
    if (disabledProxies) disabledProxies.textContent = stats.disabled_proxies;
    if (totalProxies) totalProxies.textContent = stats.total_proxies;
}

// 更新性能统计
function updatePerformanceStats(performance) {
    const totalRequests = document.getElementById('total-requests');
    const successRate = document.getElementById('success-rate');
    const avgResponse = document.getElementById('avg-response');
    
    if (totalRequests) totalRequests.textContent = performance.total_requests;
    
    if (successRate) {
        const rate = performance.total_requests > 0 ? 
            (performance.success_requests / performance.total_requests * 100).toFixed(1) : 0;
        successRate.textContent = rate + '%';
    }
    
    if (avgResponse) {
        avgResponse.textContent = performance.avg_response_time.toFixed(1) + 'ms';
    }
}

// 更新健康状态
function updateHealthStatus(health) {
    const healthStatus = document.getElementById('health-status');
    const healthIssues = document.getElementById('health-issues');
    
    if (healthStatus) {
        healthStatus.textContent = getHealthStatusText(health.status);
        healthStatus.className = 'badge ' + getHealthStatusClass(health.status);
    }
    
    if (healthIssues) {
        if (health.issues && health.issues.length > 0) {
            healthIssues.innerHTML = health.issues.map(issue => 
                `<div class="text-warning">⚠ ${issue}</div>`
            ).join('');
        } else {
            healthIssues.innerHTML = '<div class="text-success">✓ 系统运行正常</div>';
        }
    }
}

// 获取健康状态文本
function getHealthStatusText(status) {
    switch (status) {
        case 'healthy': return '健康';
        case 'warning': return '警告';
        case 'error': return '错误';
        default: return '未知';
    }
}

// 获取健康状态样式类
function getHealthStatusClass(status) {
    switch (status) {
        case 'healthy': return 'bg-success';
        case 'warning': return 'bg-warning';
        case 'error': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

// 更新性能图表
function updatePerformanceChart(stats) {
    if (!performanceChart) return;
    
    const now = new Date().toLocaleTimeString();
    const maxDataPoints = 20; // 最多显示20个数据点
    
    // 添加新数据点
    performanceChart.data.labels.push(now);
    performanceChart.data.datasets[0].data.push(Math.min(stats.cpu_usage, 100));
    performanceChart.data.datasets[1].data.push(stats.memory_usage);
    performanceChart.data.datasets[2].data.push(stats.online_connections);
    
    // 保持数据点数量不超过最大值
    if (performanceChart.data.labels.length > maxDataPoints) {
        performanceChart.data.labels.shift();
        performanceChart.data.datasets.forEach(dataset => {
            dataset.data.shift();
        });
    }
    
    performanceChart.update('none'); // 不使用动画以提高性能
}

// 导出数据
function exportDashboardData() {
    // TODO: 实现数据导出功能
    showSuccess('数据导出功能待实现');
}

// 重置图表
function resetChart() {
    if (performanceChart) {
        performanceChart.data.labels = [];
        performanceChart.data.datasets.forEach(dataset => {
            dataset.data = [];
        });
        performanceChart.update();
    }
}

// 当切换到仪表板标签页时初始化
function onDashboardTabShow() {
    if (!performanceChart) {
        initDashboard();
    } else {
        startDashboardRefresh();
    }
}

// 当离开仪表板标签页时停止刷新
function onDashboardTabHide() {
    stopDashboardRefresh();
}
