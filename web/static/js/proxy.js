// ===== 代理管理相关函数 =====

// 刷新代理列表
async function refreshProxies() {
    try {
        const response = await fetch('/api/proxies');
        const result = await response.json();
        if (result.code === 0) {
            updateProxyList(result.data);
            updateProxyGroupFilter(result.data);
        } else {
            showError('获取代理列表失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 更新代理列表
function updateProxyList(proxies) {
    const tbody = document.getElementById('proxyList');
    tbody.innerHTML = '';
    
    proxies.forEach(proxy => {
        const tr = document.createElement('tr');
        const statusBadge = getProxyStatusBadge(proxy.status);
        
        tr.innerHTML = `
            <td>${proxy.name}</td>
            <td>${proxy.hosts}</td>
            <td>${proxy.port}</td>
            <td>${proxy.group || '-'}</td>
            <td>${statusBadge}</td>
            <td>${proxy.success_count || 0}/${proxy.fail_count || 0}</td>
            <td>
                <button class="btn btn-sm btn-warning" onclick="editProxy(${proxy.id})">编辑</button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteProxy(${proxy.id})">删除</button>
                <button class="btn btn-sm btn-info" onclick="testProxy(${proxy.id})">测试</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 获取代理状态徽章
function getProxyStatusBadge(status) {
    switch (status) {
        case 'healthy':
            return '<span class="badge bg-success">健康</span>';
        case 'unhealthy':
            return '<span class="badge bg-danger">不健康</span>';
        case 'disabled':
            return '<span class="badge bg-secondary">已禁用</span>';
        default:
            return '<span class="badge bg-warning">未知</span>';
    }
}

// 更新代理分组筛选器
function updateProxyGroupFilter(proxies) {
    const select = document.getElementById('proxyGroupFilter');
    const groups = new Set(proxies.map(proxy => proxy.group).filter(Boolean));
    
    // 保留"全部分组"选项
    select.innerHTML = '<option value="">全部分组</option>';
    
    groups.forEach(group => {
        const option = document.createElement('option');
        option.value = group;
        option.textContent = group;
        select.appendChild(option);
    });
}

// 显示创建代理模态框
function showCreateProxyModal() {
    const modal = new bootstrap.Modal(document.getElementById('createProxyModal'));
    modal.show();
}

// 创建代理配置
async function createProxy() {
    const form = document.getElementById('createProxyForm');
    const formData = new FormData(form);
    
    const data = {
        name: formData.get('name'),
        hosts: formData.get('hosts'),
        port: formData.get('port'),
        username: formData.get('username'),
        password: formData.get('password'),
        group: formData.get('group'),
        status: formData.get('status')
    };

    try {
        const response = await fetch('/api/proxies', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('代理配置创建成功');
            bootstrap.Modal.getInstance(document.getElementById('createProxyModal')).hide();
            form.reset();
            refreshProxies();
        } else {
            showError(result.message || '创建代理配置失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 删除代理配置
async function deleteProxy(id) {
    if (!confirm('确定要删除该代理配置吗？')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/proxies/${id}`, {
            method: 'DELETE'
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('代理配置删除成功');
            refreshProxies();
        } else {
            showError(result.message || '删除代理配置失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 测试代理连接
async function testProxy(id) {
    showSuccess('代理测试功能待实现');
    // TODO: 实现代理测试功能
}

// 编辑代理配置
function editProxy(id) {
    showSuccess('代理编辑功能待实现');
    // TODO: 实现代理编辑功能
}

// 按状态筛选代理
async function filterProxiesByStatus() {
    const status = document.getElementById('proxyStatusFilter').value;
    
    try {
        let url = '/api/proxies';
        if (status) {
            url = `/api/proxies/status/${status}`;
        }
        
        const response = await fetch(url);
        const result = await response.json();
        if (result.code === 0) {
            updateProxyList(result.data);
        } else {
            showError('筛选代理失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 按分组筛选代理
async function filterProxiesByGroup() {
    const group = document.getElementById('proxyGroupFilter').value;
    
    try {
        let url = '/api/proxies';
        if (group) {
            url = `/api/proxies/group/${group}`;
        }
        
        const response = await fetch(url);
        const result = await response.json();
        if (result.code === 0) {
            updateProxyList(result.data);
        } else {
            showError('筛选代理失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// ===== 录制管理增强函数 =====

// 更新录制列表（增强版）
function updateRecordingList(recordings) {
    const tbody = document.getElementById('recordingList');
    if (!tbody) return; // 如果不在录制标签页则跳过
    
    tbody.innerHTML = '';
    
    recordings.forEach(recording => {
        const tr = document.createElement('tr');
        const eventCount = recording.events ? recording.events.length : 0;
        const createdAt = new Date(recording.created_at).toLocaleString();
        
        tr.innerHTML = `
            <td>${recording.id}</td>
            <td>${recording.name}</td>
            <td>${recording.description || '-'}</td>
            <td>${eventCount}</td>
            <td>${createdAt}</td>
            <td>
                <button class="btn btn-sm btn-success" onclick="playRecordingById(${recording.id})">播放</button>
                <button class="btn btn-sm btn-warning" onclick="editRecording(${recording.id})">编辑</button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteRecording(${recording.id})">删除</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 播放指定录制
async function playRecordingById(recordingId) {
    const group = document.getElementById('playGroupSelect').value;
    
    try {
        const response = await fetch('/api/recording/play', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                recording_id: recordingId,
                group: group
            })
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('开始播放录制');
        } else {
            showError('播放录制失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 播放选中的录制
function playSelectedRecording() {
    const recordingId = document.getElementById('playRecordingSelect').value;
    if (!recordingId) {
        showError('请选择要播放的录制');
        return;
    }
    
    playRecordingById(parseInt(recordingId));
}

// 删除录制
async function deleteRecording(id) {
    if (!confirm('确定要删除该录制吗？')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/recordings/${id}`, {
            method: 'DELETE'
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('录制删除成功');
            refreshRecordings();
        } else {
            showError(result.message || '删除录制失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 编辑录制
function editRecording(id) {
    showSuccess('录制编辑功能待实现');
    // TODO: 实现录制编辑功能
}

// 更新播放录制选择器
function updatePlayRecordingSelect(recordings) {
    const select = document.getElementById('playRecordingSelect');
    if (!select) return;
    
    select.innerHTML = '<option value="">选择录制...</option>';
    
    recordings.forEach(recording => {
        const option = document.createElement('option');
        option.value = recording.id;
        option.textContent = recording.name;
        select.appendChild(option);
    });
}

// 更新播放分组选择器
function updatePlayGroupSelect(clients) {
    const select = document.getElementById('playGroupSelect');
    if (!select) return;
    
    const groups = new Set(clients.map(client => client.group).filter(Boolean));
    
    select.innerHTML = '<option value="">全部客户端</option>';
    
    groups.forEach(group => {
        const option = document.createElement('option');
        option.value = group;
        option.textContent = group;
        select.appendChild(option);
    });
}
