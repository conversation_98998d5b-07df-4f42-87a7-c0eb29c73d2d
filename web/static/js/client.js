// ===== 客户端管理相关函数 =====

// 标记client.js已加载
window.clientJsLoaded = true;

// 确保在页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('client.js loaded');
});

// 显示创建客户端模态框
function showCreateClientModal() {
    const modal = new bootstrap.Modal(document.getElementById('createClientModal'));
    modal.show();
}

// 创建客户端配置
async function createClient() {
    const form = document.getElementById('createClientForm');
    const formData = new FormData(form);

    const data = {
        name: formData.get('name'),
        group: formData.get('group'),
        is_master: formData.has('is_master')
    };

    try {
        const response = await fetch('/api/clients', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('客户端配置创建成功');
            bootstrap.Modal.getInstance(document.getElementById('createClientModal')).hide();
            form.reset();
            refreshClients();
        } else {
            showError(result.message || '创建客户端配置失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 显示编辑客户端模态框
async function showEditClientModal(clientId) {
    try {
        const response = await fetch(`/api/clients/${clientId}`);
        const result = await response.json();
        if (result.code === 0) {
            const client = result.data;
            const form = document.getElementById('editClientForm');

            // 填充表单数据
            form.querySelector('input[name="client_id"]').value = client.id;
            form.querySelector('input[name="name"]').value = client.name;
            form.querySelector('input[name="group"]').value = client.group || '';
            form.querySelector('input[name="is_master"]').checked = client.is_master;

            const modal = new bootstrap.Modal(document.getElementById('editClientModal'));
            modal.show();
        } else {
            showError('获取客户端信息失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 更新客户端配置
async function updateClient() {
    const form = document.getElementById('editClientForm');
    const formData = new FormData(form);
    const clientId = formData.get('client_id');

    const data = {
        name: formData.get('name'),
        group: formData.get('group'),
        is_master: formData.has('is_master')
    };

    try {
        const response = await fetch(`/api/clients/${clientId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('客户端配置更新成功');
            bootstrap.Modal.getInstance(document.getElementById('editClientModal')).hide();
            refreshClients();
        } else {
            showError(result.message || '更新客户端配置失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 显示客户端详情
async function showClientDetail(clientId) {
    try {
        const response = await fetch(`/api/clients/${clientId}/detail`);
        const result = await response.json();
        if (result.code === 0) {
            const detail = result.data;
            const content = document.getElementById('clientDetailContent');

            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>客户端ID:</td><td>${detail.client.client_id}</td></tr>
                            <tr><td>名称:</td><td>${detail.client.name}</td></tr>
                            <tr><td>分组:</td><td>${detail.client.group || '-'}</td></tr>
                            <tr><td>类型:</td><td>${detail.client.is_master ? '主控' : '从控'}</td></tr>
                            <tr><td>在线状态:</td><td>${detail.is_online ? '<span class="badge bg-success">在线</span>' : '<span class="badge bg-secondary">离线</span>'}</td></tr>
                            <tr><td>最后在线:</td><td>${detail.client.last_online ? new Date(detail.client.last_online).toLocaleString() : '-'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>关联浏览器 (${detail.browsers.length})</h6>
                        <div class="list-group">
                            ${detail.browsers.map(browser => `
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">${browser.name}</h6>
                                        <small>${browser.group || '无分组'}</small>
                                    </div>
                                    <p class="mb-1">代理: ${browser.proxy || '无'}</p>
                                    <small>端口: ${browser.port || '默认'}</small>
                                </div>
                            `).join('')}
                            ${detail.browsers.length === 0 ? '<div class="text-muted">暂无关联浏览器</div>' : ''}
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>操作</h6>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-primary" onclick="sendCommandToClient('${detail.client.client_id}', 'ping', {})">发送Ping</button>
                            <button class="btn btn-sm btn-warning" onclick="toggleClientStatus('${detail.client.client_id}', ${!detail.client.online})">
                                ${detail.client.online ? '强制下线' : '强制上线'}
                            </button>
                        </div>
                    </div>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('clientDetailModal'));
            modal.show();
        } else {
            showError('获取客户端详情失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 切换客户端状态
async function toggleClientStatus(clientId, online) {
    try {
        const response = await fetch(`/api/clients/${clientId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ online: online })
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('客户端状态更新成功');
            refreshClients();
            // 如果详情模态框是打开的，刷新详情
            const detailModal = document.getElementById('clientDetailModal');
            if (detailModal.classList.contains('show')) {
                showClientDetail(clientId);
            }
        } else {
            showError(result.message || '更新客户端状态失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 向客户端发送命令
async function sendCommandToClient(clientId, command, data) {
    try {
        const response = await fetch(`/api/clients/${clientId}/send-command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                command: command,
                data: data
            })
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('命令发送成功');
        } else {
            showError(result.message || '发送命令失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 删除客户端
async function deleteClient(clientId) {
    if (!confirm('确定要删除该客户端吗？这将同时删除相关的浏览器配置。')) {
        return;
    }

    try {
        const response = await fetch(`/api/clients/${clientId}`, {
            method: 'DELETE'
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('客户端删除成功');
            refreshClients();
        } else {
            showError(result.message || '删除客户端失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 获取客户端分组
async function getClientGroups() {
    try {
        const response = await fetch('/api/client-groups');
        const result = await response.json();
        if (result.code === 0) {
            return result.data;
        }
    } catch (error) {
        console.error('获取客户端分组失败:', error);
    }
    return [];
}

// 更新客户端列表显示（增强版）
function updateClientList(clients) {
    console.log('client.js updateClientList called with:', clients);

    const tbody = document.getElementById('clientList');
    if (!tbody) {
        console.error('clientList table body not found');
        return;
    }

    tbody.innerHTML = '';

    clients.forEach(client => {
        const tr = document.createElement('tr');
        const statusBadge = client.online ?
            '<span class="badge bg-success">在线</span>' :
            '<span class="badge bg-secondary">离线</span>';

        const lastOnline = client.last_online ?
            new Date(client.last_online).toLocaleString() :
            '-';

        tr.innerHTML = `
            <td>${client.client_id}</td>
            <td>${client.name}</td>
            <td>${client.group || '-'}</td>
            <td>${client.is_master ? '<span class="badge bg-primary">主控</span>' : '<span class="badge bg-info">从控</span>'}</td>
            <td>${statusBadge}</td>
            <td>${lastOnline}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-info" onclick="showClientDetail('${client.client_id}')" title="查看详情">详情</button>
                    <button class="btn btn-outline-warning" onclick="showEditClientModal('${client.client_id}')" title="编辑">编辑</button>
                    <button class="btn btn-outline-danger" onclick="deleteClient('${client.client_id}')" title="删除">删除</button>
                </div>
            </td>
        `;
        tbody.appendChild(tr);
    });
}
